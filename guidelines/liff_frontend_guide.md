# Line LIFF Frontend Development Guide

## Project Setup

### 1. Initialize React Project
```bash
npx create-react-app implant-voting-liff --template typescript
cd implant-voting-liff
npm install @line/liff @supabase/supabase-js
npm install @types/react @types/react-dom
npm install react-router-dom axios react-query
npm install @headlessui/react @heroicons/react
npm install react-hook-form @hookform/resolvers yup
```

### 2. Project Structure
```
src/
├── components/
│   ├── common/
│   │   ├── Header.tsx
│   │   ├── Loading.tsx
│   │   └── ErrorBoundary.tsx
│   ├── post/
│   │   ├── CreatePost.tsx
│   │   ├── PostCard.tsx
│   │   └── ImageUpload.tsx
│   ├── voting/
│   │   ├── VotingBoard.tsx
│   │   ├── VoteForm.tsx
│   │   └── VoteSummary.tsx
│   └── profile/
│       ├── PostHistory.tsx
│       └── UserProfile.tsx
├── pages/
│   ├── HomePage.tsx
│   ├── PostPage.tsx
│   ├── BoardPage.tsx
│   ├── HistoryPage.tsx
│   └── VotePage.tsx
├── services/
│   ├── liff.ts
│   ├── supabase.ts
│   ├── api.ts
│   └── storage.ts
├── hooks/
│   ├── useLiff.ts
│   ├── useAuth.ts
│   ├── usePosts.ts
│   └── useVotes.ts
├── types/
│   ├── post.ts
│   ├── vote.ts
│   └── user.ts
└── utils/
    ├── constants.ts
    ├── validation.ts
    └── helpers.ts
```

## Core Services Setup

### 1. LIFF Service (`src/services/liff.ts`)
```typescript
import liff from '@line/liff';

class LiffService {
  private initialized = false;

  async init(): Promise<void> {
    if (this.initialized) return;
    
    try {
      await liff.init({ liffId: process.env.REACT_APP_LIFF_ID! });
      this.initialized = true;
    } catch (error) {
      console.error('LIFF initialization failed:', error);
      throw error;
    }
  }

  async login(): Promise<string> {
    if (!liff.isLoggedIn()) {
      liff.login();
      return '';
    }
    return liff.getAccessToken();
  }

  async getProfile() {
    return await liff.getProfile();
  }

  getUserId(): string {
    return liff.getContext()?.userId || '';
  }

  isInClient(): boolean {
    return liff.isInClient();
  }

  closeWindow(): void {
    if (liff.isInClient()) {
      liff.closeWindow();
    }
  }

  openWindow(url: string): void {
    liff.openWindow({ url, external: true });
  }

  sendMessage(messages: any[]): Promise<void> {
    return liff.sendMessages(messages);
  }
}

export const liffService = new LiffService();
```

### 2. Supabase Service (`src/services/supabase.ts`)
```typescript
import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/database';

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL!;
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY!;

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

// Auth service
export class AuthService {
  async signInWithLine(accessToken: string, profile: any) {
    const { data, error } = await supabase.auth.signInWithIdToken({
      provider: 'line',
      token: accessToken,
    });

    if (error) throw error;

    // Create or update user profile
    await this.upsertUserProfile(profile);
    return data;
  }

  async upsertUserProfile(profile: any) {
    const { error } = await supabase
      .from('users')
      .upsert({
        line_user_id: profile.userId,
        display_name: profile.displayName,
        profile_picture_url: profile.pictureUrl,
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'line_user_id'
      });

    if (error) throw error;
  }

  async getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  }

  async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  }
}

export const authService = new AuthService();
```

### 3. API Service (`src/services/api.ts`)
```typescript
import { supabase } from './supabase';
import { Post, Vote, VoteSummary } from '../types';

export class ApiService {
  // Posts
  async createPost(imageFile: File, description?: string): Promise<Post> {
    // Upload image first
    const imageUrl = await this.uploadImage(imageFile);
    
    const { data, error } = await supabase
      .from('posts')
      .insert({
        image_url: imageUrl,
        image_filename: imageFile.name,
        description,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getPosts(): Promise<Post[]> {
    const { data, error } = await supabase
      .from('posts')
      .select(`
        *,
        poster:users!posts_poster_id_fkey(display_name),
        votes(count)
      `)
      .eq('status', 'active')
      .order('updated_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  async getMyPosts(): Promise<Post[]> {
    const { data, error } = await supabase
      .from('posts')
      .select(`
        *,
        votes(count),
        vote_summaries(*)
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  async getPost(id: string): Promise<Post> {
    const { data, error } = await supabase
      .from('posts')
      .select(`
        *,
        votes(*),
        vote_summaries(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  }

  // Votes
  async submitVote(vote: Omit<Vote, 'id' | 'created_at'>): Promise<Vote> {
    const { data, error } = await supabase
      .from('votes')
      .insert(vote)
      .select()
      .single();

    if (error) throw error;

    // Update vote summary
    await this.updateVoteSummary(vote.post_id);
    
    return data;
  }

  async getVoteSummary(postId: string): Promise<VoteSummary[]> {
    const { data, error } = await supabase
      .from('vote_summaries')
      .select('*')
      .eq('post_id', postId)
      .order('vote_count', { ascending: false });

    if (error) throw error;
    return data;
  }

  async hasUserVoted(postId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('votes')
      .select('id')
      .eq('post_id', postId)
      .limit(1);

    if (error) throw error;
    return data.length > 0;
  }

  // Image upload
  async uploadImage(file: File): Promise<string> {
    const fileName = `${Date.now()}_${file.name}`;
    const { data, error } = await supabase.storage
      .from('xray-images')
      .upload(fileName, file);

    if (error) throw error;

    const { data: { publicUrl } } = supabase.storage
      .from('xray-images')
      .getPublicUrl(data.path);

    return publicUrl;
  }

  // Update vote summary (trigger calculation)
  private async updateVoteSummary(postId: string): Promise<void> {
    const { error } = await supabase.rpc('update_vote_summary', {
      post_id: postId
    });

    if (error) throw error;
  }
}

export const apiService = new ApiService();
```

## React Hooks

### 1. LIFF Hook (`src/hooks/useLiff.ts`)
```typescript
import { useState, useEffect } from 'react';
import { liffService } from '../services/liff';

export const useLiff = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initLiff = async () => {
      try {
        await liffService.init();
        setIsInitialized(true);
        
        if (liffService.isLoggedIn()) {
          setIsLoggedIn(true);
          const userProfile = await liffService.getProfile();
          setProfile(userProfile);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'LIFF initialization failed');
      } finally {
        setLoading(false);
      }
    };

    initLiff();
  }, []);

  const login = async () => {
    try {
      const accessToken = await liffService.login();
      if (accessToken) {
        setIsLoggedIn(true);
        const userProfile = await liffService.getProfile();
        setProfile(userProfile);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed');
    }
  };

  return {
    isInitialized,
    isLoggedIn,
    profile,
    loading,
    error,
    login,
    liff: liffService,
  };
};
```

### 2. Posts Hook (`src/hooks/usePosts.ts`)
```typescript
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiService } from '../services/api';
import { Post } from '../types';

export const usePosts = () => {
  return useQuery<Post[]>('posts', apiService.getPosts, {
    refetchInterval: 30000, // Refetch every 30 seconds
  });
};

export const useMyPosts = () => {
  return useQuery<Post[]>('myPosts', apiService.getMyPosts);
};

export const usePost = (id: string) => {
  return useQuery<Post>(['post', id], () => apiService.getPost(id));
};

export const useCreatePost = () => {
  const queryClient = useQueryClient();
  
  return useMutation(
    ({ imageFile, description }: { imageFile: File; description?: string }) =>
      apiService.createPost(imageFile, description),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('posts');
        queryClient.invalidateQueries('myPosts');
      },
    }
  );
};
```

## Key Components

### 1. Image Upload Component (`src/components/post/ImageUpload.tsx`)
```typescript
import React, { useState, useRef } from 'react';
import { PhotoIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface ImageUploadProps {
  onImageSelect: (file: File) => void;
  selectedImage?: File;
  onImageRemove?: () => void;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageSelect,
  selectedImage,
  onImageRemove,
}) => {
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert('File size must be less than 10MB');
        return;
      }

      onImageSelect(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemove = () => {
    setPreviewUrl('');
    onImageRemove?.();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="w-full">
      {previewUrl ? (
        <div className="relative">
          <img
            src={previewUrl}
            alt="Preview"
            className="w-full h-64 object-cover rounded-lg border"
          />
          <button
            type="button"
            onClick={handleRemove}
            className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        </div>
      ) : (
        <div
          onClick={() => fileInputRef.current?.click()}
          className="w-full h-64 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-gray-400 transition-colors"
        >
          <PhotoIcon className="w-12 h-12 text-gray-400 mb-2" />
          <p className="text-gray-500 text-center">
            Click to upload X-ray image
            <br />
            <span className="text-sm">Max size: 10MB</span>
          </p>
        </div>
      )}
      
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />
    </div>
  );
};
```

### 2. Vote Form Component (`src/components/voting/VoteForm.tsx`)
```typescript
import React from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

const voteSchema = yup.object({
  implant_brand: yup.string().required('Brand is required'),
  implant_model: yup.string().required('Model is required'),
  implant_diameter: yup.string().required('Diameter is required'),
  implant_length: yup.string().required('Length is required'),
  confidence_level: yup.number().min(1).max(5).required('Confidence level is required'),
  additional_notes: yup.string(),
});

interface VoteFormData {
  implant_brand: string;
  implant_model: string;
  implant_diameter: string;
  implant_length: string;
  confidence_level: number;
  additional_notes?: string;
}

interface VoteFormProps {
  postId: string;
  onSubmit: (data: VoteFormData) => void;
  loading?: boolean;
}

export const VoteForm: React.FC<VoteFormProps> = ({
  postId,
  onSubmit,
  loading = false,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<VoteFormData>({
    resolver: yupResolver(voteSchema),
  });

  const handleFormSubmit = (data: VoteFormData) => {
    onSubmit(data);
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Implant Brand *
        </label>
        <input
          {...register('implant_brand')}
          type="text"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="e.g., Nobel Biocare, Straumann"
        />
        {errors.implant_brand && (
          <p className="text-red-500 text-sm mt-1">{errors.implant_brand.message}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Model *
        </label>
        <input
          {...register('implant_model')}
          type="text"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="e.g., Active, Replace Select"
        />
        {errors.implant_model && (
          <p className="text-red-500 text-sm mt-1">{errors.implant_model.message}</p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Diameter (mm) *
          </label>
          <select
            {...register('implant_diameter')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select</option>
            <option value="3.3">3.3mm</option>
            <option value="3.5">3.5mm</option>
            <option value="4.0">4.0mm</option>
            <option value="4.3">4.3mm</option>
            <option value="5.0">5.0mm</option>
            <option value="6.0">6.0mm</option>
          </select>
          {errors.implant_diameter && (
            <p className="text-red-500 text-sm mt-1">{errors.implant_diameter.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Length (mm) *
          </label>
          <select
            {...register('implant_length')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select</option>
            <option value="8">8mm</option>
            <option value="10">10mm</option>
            <option value="11.5">11.5mm</option>
            <option value="13">13mm</option>
            <option value="15">15mm</option>
            <option value="16">16mm</option>
          </select>
          {errors.implant_length && (
            <p className="text-red-500 text-sm mt-1">{errors.implant_length.message}</p>
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Confidence Level *
        </label>
        <div className="flex space-x-2">
          {[1, 2, 3, 4, 5].map((level) => (
            <label key={level} className="flex items-center">
              <input
                {...register('confidence_level')}
                type="radio"
                value={level}
                className="mr-1"
              />
              <span className="text-sm">{level}</span>
            </label>
          ))}
        </div>
        <p className="text-xs text-gray-500 mt-1">
          1 = Very uncertain, 5 = Very confident
        </p>
        {errors.confidence_level && (
          <p className="text-red-500 text-sm mt-1">{errors.confidence_level.message}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Additional Notes
        </label>
        <textarea
          {...register('additional_notes')}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Any additional observations or comments..."
        />
      </div>

      <button
        type="submit"
        disabled={loading}
        className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
      >
        {loading ? 'Submitting...' : 'Submit Vote'}
      </button>
    </form>
  );
};
```

## Routing and Navigation

### Main App Component (`src/App.tsx`)
```typescript
import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { useLiff } from './hooks/useLiff';
import { Loading } from './components/common/Loading';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import { HomePage } from './pages/HomePage';
import { PostPage } from './pages/PostPage';
import { BoardPage } from './pages/BoardPage';
import { HistoryPage } from './pages/HistoryPage';
import { VotePage } from './pages/VotePage';

const queryClient = new QueryClient();

function AppContent() {
  const { isInitialized, isLoggedIn, loading, error, login } = useLiff();

  if (loading) return <Loading />;
  if (error) return <div>Error: {error}</div>;
  if (!isInitialized) return <div>Initializing...</div>;
  
  if (!isLoggedIn) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Implant Voting System</h1>
          <button
            onClick={login}
            className="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600"
          >
            Login with Line
          </button>
        </div>
      </div>
    );
  }

  return (
    <BrowserRouter>
      <div className="min-h-screen bg-gray-50">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/post" element={<PostPage />} />
          <Route path="/board" element={<BoardPage />} />
          <Route path="/history" element={<HistoryPage />} />
          <Route path="/vote/:postId" element={<VotePage />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </div>
    </BrowserRouter>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AppContent />
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
```

## Environment Configuration

### `.env` file
```env
REACT_APP_LIFF_ID=your_liff_id_here
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
REACT_APP_LINE_CHANNEL_ID=your_line_channel_id
```

### Build Configuration for Line LIFF

#### `public/index.html`
```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <title>Implant Voting System</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
```

## Testing Strategy

### Unit Tests with Jest
```typescript
// src/__tests__/components/VoteForm.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { VoteForm } from '../components/voting/VoteForm';

describe('VoteForm', () => {
  it('validates required fields', async () => {
    const mockOnSubmit = jest.fn();
    render(<VoteForm postId="test" onSubmit={mockOnSubmit} />);
    
    fireEvent.click(screen.getByText('Submit Vote'));
    
    expect(await screen.findByText('Brand is required')).toBeInTheDocument();
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });
});
```

## Deployment

### Build and Deploy
```bash
# Build for production
npm run build

# Deploy to your hosting service (Vercel, Netlify, etc.)
# Make sure to set environment variables in your hosting platform
```

## Performance Optimizations

1. **Code Splitting**: Use React.lazy for route-based splitting
2. **Image Optimization**: Compress images before upload
3. **Caching**: Implement service worker for offline support
4. **Bundle Analysis**: Use webpack-bundle-analyzer
5. **Real-time Updates**: Use Supabase real-time subscriptions efficiently