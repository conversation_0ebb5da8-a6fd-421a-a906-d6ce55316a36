{
  "name": "implant-voting-liff",
  "version": "1.0.0",
  "description": "Line LIFF app for dental implant voting system",
  "private": true,
  "dependencies": {
    "@headlessui/react": "^1.7.17",
    "@heroicons/react": "^2.0.18",
    "@line/liff": "^2.22.0",
    "@supabase/supabase-js": "^2.38.4",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-dropzone": "^14.2.3",
    "react-hot-toast": "^2.4.1",
    "react-query": "^3.39.3",
    "react-router-dom": "^6.18.0",
    "react-scripts": "5.0.1"
  },
  "devDependencies": {
    "@types/react": "^18.2.37",
    "@types/react-dom": "^18.2.15",
    "autoprefixer": "^10.4.16",
    "postcss": "^8.4.31",
    "tailwindcss": "^3.3.5",
    "typescript": "^4.9.5"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject",
    "setup:all": "npm run setup:supabase && npm run setup:line",
    "setup:supabase": "node scripts/setup-supabase.js",
    "setup:line": "node scripts/setup-line.js",
    "test:liff": "node scripts/test-liff.js",
    "build:staging": "REACT_APP_ENV=staging npm run build",
    "build:prod": "REACT_APP_ENV=production npm run build",
    "deploy:vercel": "vercel --prod",
    "deploy:netlify": "netlify deploy --prod --dir=build",
    "dev": "npm start",
    "preview": "npm run build && npx serve -s build -l 3000"
  },
  "eslintConfig": {
    "extends": [
      "react-app",
      "react-app/jest"
    ]
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  "homepage": "."
}

// ==================== ENVIRONMENT FILES ====================

// .env.example - Template for environment variables
REACT_APP_SUPABASE_URL=your_supabase_project_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
REACT_APP_LIFF_ID=your_liff_app_id
REACT_APP_LINE_CHANNEL_ID=your_line_channel_id

# App Configuration
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/jpg
REACT_APP_POST_EXPIRY_HOURS=24

# Feature Flags (for MVP)
REACT_APP_ENABLE_NOTIFICATIONS=false
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_DEBUG_MODE=true

# ==================== DEPLOYMENT CONFIGURATIONS ====================

// vercel.json - Vercel deployment configuration
{
  "version": 2,
  "name": "implant-voting-liff",
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "build"
      }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ],
  "env": {
    "REACT_APP_SUPABASE_URL": "@supabase_url",
    "REACT_APP_SUPABASE_ANON_KEY": "@supabase_anon_key", 
    "REACT_APP_LIFF_ID": "@liff_id",
    "REACT_APP_LINE_CHANNEL_ID": "@line_channel_id"
  },
  "build": {
    "env": {
      "REACT_APP_SUPABASE_URL": "@supabase_url",
      "REACT_APP_SUPABASE_ANON_KEY": "@supabase_anon_key",
      "REACT_APP_LIFF_ID": "@liff_id",
      "REACT_APP_LINE_CHANNEL_ID": "@line_channel_id"
    }
  }
}

// netlify.toml - Netlify deployment configuration
[build]
  publish = "build"
  command = "npm run build"

[build.environment]
  REACT_APP_SUPABASE_URL = "your_supabase_url"
  REACT_APP_SUPABASE_ANON_KEY = "your_supabase_anon_key"
  REACT_APP_LIFF_ID = "your_liff_id"
  REACT_APP_LINE_CHANNEL_ID = "your_line_channel_id"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[context.production]
  command = "npm run build:prod"

[context.staging]
  command = "npm run build:staging"

// ==================== TAILWIND CONFIGURATION ====================

// tailwind.config.js - Optimized for mobile LIFF app
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html"
  ],
  theme: {
    extend: {
      colors: {
        line: {
          green: '#00B900',
          darkgreen: '#009900'
        },
        primary: {
          50: '#eff6ff',
          100: '#dbeafe', 
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a'
        }
      },
      fontFamily: {
        sans: ['Inter', '-apple-system', 'BlinkMacSystemFont', 'system-ui', 'sans-serif']
      },
      spacing: {
        'safe-top': 'env(safe-area-inset-top)',
        'safe-bottom': 'env(safe-area-inset-bottom)',
        'safe-left': 'env(safe-area-inset-left)',
        'safe-right': 'env(safe-area-inset-right)'
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        }
      }
    }
  },
  plugins: []
}

// postcss.config.js - PostCSS configuration
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}

// ==================== SUPABASE SETUP SQL ====================

// supabase/migrations/001_initial_schema.sql - Database setup
-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  line_user_id TEXT UNIQUE NOT NULL,
  display_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Posts table  
CREATE TABLE IF NOT EXISTS posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'expired', 'completed')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '24 hours')
);

-- Votes table
CREATE TABLE IF NOT EXISTS votes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  brand TEXT NOT NULL,
  model TEXT NOT NULL,
  diameter TEXT NOT NULL,
  length TEXT NOT NULL,
  confidence INTEGER CHECK (confidence >= 1 AND confidence <= 5),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(post_id, user_id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_posts_status_created ON posts(status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_votes_post_id ON votes(post_id);
CREATE INDEX IF NOT EXISTS idx_users_line_id ON users(line_user_id);

-- Vote summaries view
CREATE OR REPLACE VIEW vote_summaries AS
SELECT 
  post_id,
  brand,
  model,
  diameter,
  length,
  COUNT(*) as vote_count,
  ROUND(AVG(confidence::NUMERIC), 1) as avg_confidence,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (PARTITION BY post_id), 1) as percentage
FROM votes
GROUP BY post_id, brand, model, diameter, length
ORDER BY vote_count DESC;

-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE votes ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Public posts are viewable by everyone" ON posts
  FOR SELECT USING (status = 'active');

CREATE POLICY "Users can insert their own posts" ON posts
  FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update their own posts" ON posts
  FOR UPDATE USING (auth.uid()::text = user_id::text);

CREATE POLICY "Votes are viewable by everyone" ON votes
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own votes" ON votes
  FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can view and update their own profile" ON users
  FOR ALL USING (auth.uid()::text = line_user_id);

-- Storage policies (run in Supabase dashboard)
-- INSERT INTO storage.buckets (id, name, public) VALUES ('xray-images', 'xray-images', true);

-- CREATE POLICY "Anyone can view images" ON storage.objects
--   FOR SELECT USING (bucket_id = 'xray-images');

-- CREATE POLICY "Authenticated users can upload images" ON storage.objects
--   FOR INSERT WITH CHECK (bucket_id = 'xray-images' AND auth.role() = 'authenticated');

// ==================== ADDITIONAL SETUP SCRIPTS ====================

// scripts/test-liff.js - Test LIFF integration
const https = require('https');

function testLiffIntegration() {
  console.log('🧪 Testing LIFF integration...');
  
  const liffId = process.env.REACT_APP_LIFF_ID;
  
  if (!liffId) {
    console.error('❌ REACT_APP_LIFF_ID not found in environment');
    process.exit(1);
  }
  
  // Test LIFF endpoint
  const url = `https://api.line.me/liff/v1/apps/${liffId}`;
  
  https.get(url, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      if (res.statusCode === 200) {
        const liffInfo = JSON.parse(data);
        console.log('✅ LIFF app found:', liffInfo.view.url);
        console.log('📱 App type:', liffInfo.view.type);
        console.log('🔗 BLE feature:', liffInfo.features?.ble ? 'enabled' : 'disabled');
      } else {
        console.error('❌ LIFF app not found or invalid LIFF ID');
        console.log('Response:', data);
      }
    });
  }).on('error', (err) => {
    console.error('❌ Network error:', err.message);
  });
}

if (require.main === module) {
  testLiffIntegration();
}

// scripts/quick-deploy.js - One-click deployment script
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function quickDeploy(platform = 'vercel') {
  console.log(`🚀 Quick deploying to ${platform}...`);
  
  try {
    // Check if .env.local exists
    if (!fs.existsSync('.env.local')) {
      console.error('❌ .env.local not found. Run setup scripts first.');
      process.exit(1);
    }
    
    // Build the project
    console.log('📦 Building project...');
    execSync('npm run build', { stdio: 'inherit' });
    
    // Deploy based on platform
    switch (platform.toLowerCase()) {
      case 'vercel':
        console.log('🔼 Deploying to Vercel...');
        execSync('npx vercel --prod', { stdio: 'inherit' });
        break;
        
      case 'netlify':
        console.log('🌐 Deploying to Netlify...');
        execSync('npx netlify deploy --prod --dir=build', { stdio: 'inherit' });
        break;
        
      default:
        console.error('❌ Unsupported platform. Use: vercel or netlify');
        process.exit(1);
    }
    
    console.log('✅ Deployment complete!');
    console.log('📝 Don\'t forget to update your LIFF app endpoint URL');
    
  } catch (error) {
    console.error('❌ Deployment failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  const platform = process.argv[2] || 'vercel';
  quickDeploy(platform);
}

// ==================== PUBLIC HTML TEMPLATE ====================

// public/index.html - Optimized for Line LIFF
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
    <meta name="theme-color" content="#3B82F6" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Implant Voting" />
    
    <title>Implant Voting System</title>
    <meta name="description" content="Get expert opinions on dental implant identification" />
    
    <!-- Line LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <!-- Prevent zoom on iOS -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    
    <!-- PWA meta tags -->
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <style>
      /* Prevent iOS bounce scroll */
      body {
        overscroll-behavior: none;
        -webkit-overflow-scrolling: touch;
        position: fixed;
        width: 100%;
        height: 100vh;
        overflow: hidden;
      }
      
      #root {
        width: 100%;
        height: 100%;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
      }
      
      /* Loading screen */
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f9fafb;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    
    <!-- Loading screen -->
    <div id="loading-screen" class="loading-screen">
      <div class="loading-spinner"></div>
    </div>
    
    <div id="root"></div>
    
    <script>
      // Hide loading screen when React loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.display = 'none';
          }
        }, 1000);
      });
    </script>
  </body>
</html>

// public/manifest.json - PWA manifest
{
  "short_name": "Implant Voting",
  "name": "Implant Voting System",
  "description": "Get expert opinions on dental implant identification",
  "icons": [
    {
      "src": "favicon.ico",
      "sizes": "64x64 32x32 24x24 16x16",
      "type": "image/x-icon"
    },
    {
      "src": "logo192.png",
      "type": "image/png", 
      "sizes": "192x192"
    },
    {
      "src": "logo512.png",
      "type": "image/png",
      "sizes": "512x512"
    }
  ],
  "start_url": ".",
  "display": "standalone",
  "theme_color": "#3B82F6",
  "background_color": "#F9FAFB",
  "orientation": "portrait"
}

// ==================== README INSTRUCTIONS ====================

// README.md - Quick start guide
# 🦷 Implant Voting System - Line LIFF App

## 🚀 Quick Start (5 minutes)

### 1. Clone and Install
```bash
git clone <repo-url> implant-voting
cd implant-voting
npm install
```

### 2. Setup Environment
```bash
cp .env.example .env.local
# Edit .env.local with your credentials
```

### 3. Run Setup Scripts
```bash
npm run setup:all
```

### 4. Start Development
```bash
npm start
```

## 📱 Features
- ✅ Line LIFF authentication
- ✅ Image upload (drag & drop)
- ✅ Dental implant voting system
- ✅ Real-time vote results
- ✅ Mobile-optimized UI
- ✅ 24-hour post expiration

## 🛠 Tech Stack
- **Frontend**: React + TypeScript + Tailwind CSS
- **Backend**: Supabase (Database + Storage + Auth)
- **Integration**: Line LIFF SDK
- **Deployment**: Vercel / Netlify

## 🚢 Deployment

### Vercel (Recommended)
```bash
npm run deploy:vercel
```

### Netlify
```bash
npm run deploy:netlify
```

## 📋 Setup Checklist
- [ ] Supabase project created
- [ ] Line Developer account setup
- [ ] LIFF app configured
- [ ] Environment variables set
- [ ] Database schema deployed
- [ ] Storage bucket created
- [ ] App deployed and tested

## 🔧 Development Commands
- `npm start` - Start development server
- `npm run build` - Build for production
- `npm run setup:all` - Run all setup scripts
- `npm run test:liff` - Test Line integration

## 📖 Documentation
- [Supabase Setup Guide](./docs/supabase-setup.md)
- [Line LIFF Guide](./docs/line-setup.md)
- [Deployment Guide](./docs/deployment.md)

## 🆘 Troubleshooting
1. **LIFF not loading**: Check LIFF ID and endpoint URL
2. **Database errors**: Verify Supabase credentials
3. **Upload failing**: Check storage bucket policies
4. **Build errors**: Clear node_modules and reinstall

## 📞 Support
- Check [Issues](../../issues) for common problems
- Join our [Discord](discord-link) for help
- Email: <EMAIL>