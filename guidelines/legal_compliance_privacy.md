# Legal Compliance & Privacy Implementation

## Medical Data Protection Framework

### HIPAA Compliance (US)

#### Technical Safeguards
```typescript
// services/hipaaCompliance.ts
class HIPAAComplianceService {
  // Minimum necessary standard
  static filterDataByRole(data: any, userRole: UserRole): any {
    const allowedFields = this.getAllowedFieldsByRole(userRole);
    return Object.keys(data)
      .filter(key => allowedFields.includes(key))
      .reduce((obj, key) => {
        obj[key] = data[key];
        return obj;
      }, {} as any);
  }

  // Audit logging for medical data access
  static async logDataAccess(event: DataAccessEvent): Promise<void> {
    await supabase.from('audit_logs').insert({
      user_id: event.userId,
      action: event.action,
      resource_type: event.resourceType,
      resource_id: event.resourceId,
      ip_address: event.ipAddress,
      user_agent: event.userAgent,
      timestamp: new Date().toISOString(),
      details: event.details,
    });
  }

  // De-identification of medical images
  static async deidentifyImage(imageFile: File): Promise<File> {
    // Remove EXIF data that might contain patient information
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        
        // Convert to blob without metadata
        canvas.toBlob((blob) => {
          if (blob) {
            const deidentifiedFile = new File(
              [blob], 
              `deidentified_${Date.now()}.jpg`,
              { type: 'image/jpeg' }
            );
            resolve(deidentifiedFile);
          }
        }, 'image/jpeg', 0.9);
      };
      img.src = URL.createObjectURL(imageFile);
    });
  }

  // Encryption key management
  static async generateEncryptionKey(): Promise<CryptoKey> {
    return await crypto.subtle.generateKey(
      { name: 'AES-GCM', length: 256 },
      false, // Not extractable
      ['encrypt', 'decrypt']
    );
  }

  // Secure data transmission
  static async encryptSensitiveData(data: string): Promise<EncryptedData> {
    const key = await this.generateEncryptionKey();
    const iv = crypto.getRandomValues(new Uint8Array(12));
    
    const encoded = new TextEncoder().encode(data);
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      key,
      encoded
    );

    return {
      data: Array.from(new Uint8Array(encrypted)),
      iv: Array.from(iv),
      algorithm: 'AES-GCM'
    };
  }
}

interface DataAccessEvent {
  userId: string;
  action: 'view' | 'create' | 'update' | 'delete';
  resourceType: 'post' | 'vote' | 'user_profile';
  resourceId: string;
  ipAddress: string;
  userAgent: string;
  details?: Record<string, any>;
}

interface EncryptedData {
  data: number[];
  iv: number[];
  algorithm: string;
}
```

#### Administrative Safeguards
```sql
-- Database-level audit triggers
CREATE OR REPLACE FUNCTION audit_trigger_function() 
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO audit_logs (
    table_name,
    operation,
    old_data,
    new_data,
    user_id,
    timestamp
  ) VALUES (
    TG_TABLE_NAME,
    TG_OP,
    CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
    CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN row_to_json(NEW) ELSE NULL END,
    auth.uid(),
    NOW()
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Apply to all medical data tables
CREATE TRIGGER posts_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON posts
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER votes_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON votes
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
```

### GDPR Compliance (EU)

#### Data Processing Legal Basis
```typescript
// types/gdpr.ts
export enum LegalBasis {
  CONSENT = 'consent',
  CONTRACT = 'contract',
  LEGAL_OBLIGATION = 'legal_obligation',
  VITAL_INTERESTS = 'vital_interests',
  PUBLIC_TASK = 'public_task',
  LEGITIMATE_INTERESTS = 'legitimate_interests'
}

export interface DataProcessingRecord {
  purpose: string;
  legalBasis: LegalBasis;
  dataCategories: string[];
  recipients: string[];
  retentionPeriod: string;
  crossBorderTransfers: boolean;
  securityMeasures: string[];
}

// Data processing registry
export const DATA_PROCESSING_ACTIVITIES: DataProcessingRecord[] = [
  {
    purpose: 'Dental implant identification and professional consultation',
    legalBasis: LegalBasis.LEGITIMATE_INTERESTS,
    dataCategories: [
      'Professional credentials',
      'Medical imaging data (anonymized)',
      'Professional opinions and assessments',
      'Usage analytics'
    ],
    recipients: [
      'Verified dental professionals',
      'System administrators',
      'Technical support staff'
    ],
    retentionPeriod: '2 years after account closure',
    crossBorderTransfers: true,
    securityMeasures: [
      'End-to-end encryption',
      'Access controls',
      'Audit logging',
      'Regular security assessments'
    ]
  }
];
```

#### User Rights Implementation
```typescript
// services/gdprService.ts
class GDPRService {
  // Right to be informed
  static async getPrivacyNotice(): Promise<PrivacyNotice> {
    return {
      dataController: {
        name: 'Implant Voting System',
        contact: '<EMAIL>',
        address: 'Your company address'
      },
      processingPurposes: DATA_PROCESSING_ACTIVITIES,
      userRights: [
        'Right of access',
        'Right to rectification',
        'Right to erasure',
        'Right to restrict processing',
        'Right to data portability',
        'Right to object'
      ],
      dataRetentionPeriods: {
        userProfiles: '2 years after account deletion',
        medicalImages: 'Deleted immediately upon request',
        votingData: '2 years for statistical purposes',
        auditLogs: '7 years for compliance'
      }
    };
  }

  // Right of access (Article 15)
  static async exportUserData(userId: string): Promise<UserDataExport> {
    const [profile, posts, votes, activities] = await Promise.all([
      this.getUserProfile(userId),
      this.getUserPosts(userId),
      this.getUserVotes(userId),
      this.getUserActivities(userId)
    ]);

    return {
      exportDate: new Date().toISOString(),
      user: profile,
      posts: posts.map(post => ({
        ...post,
        image_url: '[REDACTED - Available on request]' // Don't include in export
      })),
      votes: votes,
      activities: activities,
      format: 'JSON',
      version: '1.0'
    };
  }

  // Right to erasure (Article 17)
  static async deleteUserData(userId: string, reason: DeletionReason): Promise<DeletionReport> {
    const deletionId = crypto.randomUUID();
    
    try {
      // Start transaction
      const { error } = await supabase.rpc('begin_user_deletion', {
        user_id: userId,
        deletion_id: deletionId,
        reason: reason
      });

      if (error) throw error;

      // Log deletion request
      await this.logDeletionRequest(userId, deletionId, reason);

      // Schedule background deletion (to handle related data)
      await this.scheduleDataDeletion(userId, deletionId);

      return {
        deletionId,
        status: 'initiated',
        estimatedCompletion: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        dataTypes: ['profile', 'posts', 'votes', 'activities'],
        note: 'Some data may be retained for legal compliance purposes'
      };
    } catch (error) {
      await this.logDeletionError(userId, deletionId, error);
      throw error;
    }
  }

  // Right to data portability (Article 20)
  static async generatePortableData(userId: string): Promise<PortableDataPackage> {
    const userData = await this.exportUserData(userId);
    
    return {
      ...userData,
      format: 'JSON-LD', // Structured data format
      schema: 'https://schema.org/Person',
      machineReadable: true,
      downloadUrl: await this.generateSecureDownloadUrl(userId),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    };
  }

  // Consent management
  static async recordConsent(consent: ConsentRecord): Promise<void> {
    await supabase.from('consent_records').insert({
      user_id: consent.userId,
      purpose: consent.purpose,
      consent_given: consent.given,
      consent_date: new Date().toISOString(),
      consent_method: consent.method,
      consent_evidence: consent.evidence,
      legal_basis: consent.legalBasis
    });
  }

  static async withdrawConsent(userId: string, purpose: string): Promise<void> {
    await this.recordConsent({
      userId,
      purpose,
      given: false,
      method: 'user_withdrawal',
      evidence: 'User initiated withdrawal',
      legalBasis: LegalBasis.CONSENT
    });

    // Take appropriate action based on withdrawn consent
    await this.handleConsentWithdrawal(userId, purpose);
  }
}

interface ConsentRecord {
  userId: string;
  purpose: string;
  given: boolean;
  method: 'explicit' | 'implied' | 'user_withdrawal';
  evidence: string;
  legalBasis: LegalBasis;
}

interface DeletionReport {
  deletionId: string;
  status: 'initiated' | 'in_progress' | 'completed' | 'failed';
  estimatedCompletion: Date;
  dataTypes: string[];
  note?: string;
}

enum DeletionReason {
  USER_REQUEST = 'user_request',
  ACCOUNT_CLOSURE = 'account_closure',
  GDPR_COMPLIANCE = 'gdpr_compliance',
  DATA_RETENTION_POLICY = 'data_retention_policy'
}
```

### Medical Device Regulations

#### FDA Medical Device Software Guidance
```typescript
// compliance/medicalDevice.ts
class MedicalDeviceCompliance {
  // Software as Medical Device (SaMD) classification
  static getSaMDClassification(): SaMDClassification {
    return {
      category: 'Class I', // Lowest risk category
      healthcareSituation: 'inform', // Provides information to healthcare professionals
      stateOfHealthcare: 'serious', // Serious healthcare situation
      riskLevel: 'low',
      regulatoryRequirements: [
        'Quality management system',
        'Clinical evaluation',
        'Risk management',
        'Software lifecycle processes'
      ]
    };
  }

  // Clinical validation requirements
  static async validateClinicalAccuracy(): Promise<ValidationReport> {
    // This would involve clinical studies comparing:
    // 1. Expert consensus through the app
    // 2. Gold standard implant identification methods
    // 3. Statistical analysis of accuracy rates
    
    return {
      studyDesign: 'retrospective_observational',
      sampleSize: 1000,
      primaryEndpoint: 'diagnostic_accuracy',
      accuracyRate: 0.92, // 92% accuracy
      confidenceInterval: [0.89, 0.95],
      statisticalSignificance: true,
      clinicalSignificance: true,
      limitations: [
        'Observer bias in image quality',
        'Variability in implant visibility',
        'Limited to specific implant types'
      ]
    };
  }

  // Risk management (ISO 14971)
  static async performRiskAnalysis(): Promise<RiskAnalysis> {
    return {
      identifiedRisks: [
        {
          id: 'R001',
          description: 'Incorrect implant identification leading to inappropriate treatment',
          probability: 'unlikely',
          severity: 'serious',
          riskLevel: 'medium',
          mitigations: [
            'Multiple expert consensus required',
            'Confidence level indicators',
            'Disclaimer about consulting additional sources'
          ]
        },
        {
          id: 'R002',
          description: 'Patient data privacy breach',
          probability: 'unlikely',
          severity: 'serious',
          riskLevel: 'medium',
          mitigations: [
            'Data anonymization',
            'Encryption at rest and in transit',
            'Access controls and audit logs'
          ]
        }
      ],
      residualRisk: 'acceptable',
      riskManagementFile: 'maintained',
      postMarketSurveillance: 'planned'
    };
  }
}

interface SaMDClassification {
  category: string;
  healthcareSituation: string;
  stateOfHealthcare: string;
  riskLevel: string;
  regulatoryRequirements: string[];
}
```

## Privacy-by-Design Implementation

### Data Minimization
```typescript
// utils/dataMinimization.ts
class DataMinimizationService {
  // Collect only necessary data
  static filterUserRegistrationData(rawData: any): UserRegistrationData {
    return {
      // Only collect essential professional information
      lineUserId: rawData.lineUserId,
      displayName: rawData.displayName,
      // Remove unnecessary personal data
      professionalCredentials: rawData.professionalCredentials,
      specialization: rawData.specialization,
      yearsExperience: rawData.yearsExperience
      // Exclude: birthdate, personal address, phone, etc.
    };
  }

  // Anonymize posts immediately
  static anonymizePost(post: Post): AnonymizedPost {
    return {
      id: post.id,
      imageUrl: post.imageUrl,
      description: post.description,
      createdAt: post.createdAt,
      // Remove all identifying information
      poster: {
        experienceLevel: this.categorizeExperience(post.poster.yearsExperience),
        specialization: post.poster.specialization,
        // No names, locations, or specific identifiers
      },
      votes: post.votes?.map(vote => ({
        id: vote.id,
        implantAssessment: vote.implantAssessment,
        confidence: vote.confidence,
        // Remove voter identity
        voterCredentials: this.categorizeCredentials(vote.voter.credentials)
      }))
    };
  }

  // Purpose limitation
  static validateDataUsage(data: any, purpose: DataUsagePurpose): boolean {
    const allowedDataTypes = this.getAllowedDataTypesForPurpose(purpose);
    const requestedDataTypes = Object.keys(data);
    
    return requestedDataTypes.every(type => allowedDataTypes.includes(type));
  }

  // Storage limitation
  static async cleanupExpiredData(): Promise<CleanupReport> {
    const cleanupActions = await Promise.all([
      this.deleteExpiredPosts(),
      this.anonymizeOldUserSessions(),
      this.archiveOldAuditLogs(),
      this.removeTemporaryFiles()
    ]);

    return {
      timestamp: new Date().toISOString(),
      actions: cleanupActions,
      totalItemsProcessed: cleanupActions.reduce((sum, action) => sum + action.count, 0),
      retentionPoliciesApplied: [
        'Posts: 2 years',
        'User sessions: 30 days',
        'Audit logs: 7 years (archived)',
        'Temporary files: 24 hours'
      ]
    };
  }
}

enum DataUsagePurpose {
  IMPLANT_IDENTIFICATION = 'implant_identification',
  PROFESSIONAL_NETWORKING = 'professional_networking',
  SYSTEM_ANALYTICS = 'system_analytics',
  SECURITY_MONITORING = 'security_monitoring',
  LEGAL_COMPLIANCE = 'legal_compliance'
}
```

### Consent Management UI
```typescript
// components/ConsentManager.tsx
const ConsentManager: React.FC = () => {
  const [consents, setConsents] = useState<ConsentState>({});
  const [loading, setLoading] = useState(true);

  const consentTypes = [
    {
      id: 'essential',
      title: 'Essential Services',
      description: 'Required for basic app functionality and security',
      required: true,
      purposes: ['Authentication', 'Security monitoring', 'Error tracking']
    },
    {
      id: 'functional',
      title: 'Enhanced Features',
      description: 'Enables additional features like personalized recommendations',
      required: false,
      purposes: ['User preferences', 'Feature customization', 'Usage optimization']
    },
    {
      id: 'analytics',
      title: 'Analytics and Improvement',
      description: 'Helps us understand usage patterns and improve the service',
      required: false,
      purposes: ['Usage analytics', 'Performance monitoring', 'Feature development']
    },
    {
      id: 'marketing',
      title: 'Communications',
      description: 'Allows us to send you updates about new features and services',
      required: false,
      purposes: ['Product updates', 'Educational content', 'Service announcements']
    }
  ];

  const handleConsentChange = async (consentId: string, granted: boolean) => {
    setConsents(prev => ({ ...prev, [consentId]: granted }));
    
    await GDPRService.recordConsent({
      userId: currentUser.id,
      purpose: consentId,
      given: granted,
      method: 'explicit',
      evidence: `User toggle in consent manager`,
      legalBasis: LegalBasis.CONSENT
    });
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Privacy Preferences</h2>
        <p className="text-gray-600">
          Control how your data is used. You can change these settings at any time.
        </p>
      </div>

      <div className="space-y-6">
        {consentTypes.map(consent => (
          <div key={consent.id} className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {consent.title}
                  {consent.required && (
                    <span className="ml-2 text-sm text-red-600">(Required)</span>
                  )}
                </h3>
                <p className="text-gray-600 mb-3">{consent.description}</p>
                
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Used for:</h4>
                  <ul className="text-sm text-gray-600 list-disc list-inside">
                    {consent.purposes.map(purpose => (
                      <li key={purpose}>{purpose}</li>
                    ))}
                  </ul>
                </div>
              </div>
              
              <div className="ml-4">
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={consents[consent.id] ?? consent.required}
                    onChange={(e) => handleConsentChange(consent.id, e.target.checked)}
                    disabled={consent.required}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 pt-6 border-t border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Your Rights</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button className="text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
            <h4 className="font-medium text-gray-900">Download Your Data</h4>
            <p className="text-sm text-gray-600 mt-1">Export all your data in a portable format</p>
          </button>
          
          <button className="text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
            <h4 className="font-medium text-gray-900">Delete Account</h4>
            <p className="text-sm text-gray-600 mt-1">Permanently remove your account and data</p>
          </button>
          
          <button className="text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
            <h4 className="font-medium text-gray-900">Correct Information</h4>
            <p className="text-sm text-gray-600 mt-1">Update or correct your personal information</p>
          </button>
          
          <button className="text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
            <h4 className="font-medium text-gray-900">Contact Privacy Team</h4>
            <p className="text-sm text-gray-600 mt-1">Questions about your privacy and data</p>
          </button>
        </div>
      </div>
    </div>
  );
};
```

## Terms of Service & Privacy Policy

### Medical Disclaimer
```typescript
// Legal disclaimer component
const MedicalDisclaimer: React.FC = () => (
  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
    <div className="flex items-start">
      <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
      <div>
        <h3 className="text-lg font-medium text-yellow-800 mb-2">
          Medical Professional Use Only
        </h3>
        <div className="text-sm text-yellow-700 space-y-2">
          <p>
            This application is intended for use by qualified dental professionals only. 
            The information provided is for educational and consultation purposes.
          </p>
          <p>
            <strong>Important:</strong> This tool does not replace professional judgment, 
            clinical examination, or established diagnostic procedures. Always verify 
            implant identification through multiple sources and manufacturer documentation.
          </p>
          <p>
            Users are responsible for ensuring compliance with local regulations 
            regarding medical device identification and patient care standards.
          </p>
        </div>
      </div>
    </div>
  </div>
);
```

This comprehensive legal compliance framework ensures your Line LIFF implant voting system meets international privacy standards and medical device regulations while protecting both user data and the platform from legal risks.