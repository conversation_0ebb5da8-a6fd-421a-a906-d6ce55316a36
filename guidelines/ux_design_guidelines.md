# UX/UI Design Guidelines & Mobile Optimization

## Design Philosophy

### Core Principles
1. **Medical Professional First**: Design for busy healthcare professionals who need quick, accurate information
2. **Mobile-Native**: Optimized for Line's mobile environment with touch-first interactions
3. **Trust & Credibility**: Clean, professional interface that inspires confidence in medical decisions
4. **Accessibility**: Usable by professionals of all ages and technical skill levels
5. **Speed & Efficiency**: Minimize cognitive load and interaction time

### Visual Identity

#### Color Palette
```css
/* Primary Colors */
--primary-50: #eff6ff;
--primary-500: #3b82f6;   /* Line-friendly blue */
--primary-600: #2563eb;
--primary-700: #1d4ed8;

/* Medical Theme */
--medical-blue: #0066cc;
--medical-green: #00b894;
--medical-red: #e74c3c;
--medical-orange: #f39c12;

/* Grayscale */
--gray-50: #f9fafb;
--gray-100: #f3f4f6;
--gray-500: #6b7280;
--gray-900: #111827;

/* Status Colors */
--success: #10b981;
--warning: #f59e0b;
--error: #ef4444;
--info: #3b82f6;
```

#### Typography Scale
```css
/* Font Stack - Line-optimized */
font-family: 
  'Hiragino Sans', 
  'ヒラギノ角ゴシック ProN', 
  'Hiragino Kaku Gothic ProN',
  'Yu Gothic', 
  '游ゴシック', 
  Meiryo, 
  'MS Pゴシック',
  sans-serif;

/* Scale */
--text-xs: 0.75rem;    /* 12px - Captions */
--text-sm: 0.875rem;   /* 14px - Body text */
--text-base: 1rem;     /* 16px - Default */
--text-lg: 1.125rem;   /* 18px - Subtitles */
--text-xl: 1.25rem;    /* 20px - Titles */
--text-2xl: 1.5rem;    /* 24px - Headers */
```

## Mobile-First Design Patterns

### Touch Targets & Spacing
```css
/* Minimum touch target sizes */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 16px;
}

/* Safe area handling for notched devices */
.safe-area {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Thumb-friendly zones */
.thumb-zone {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px; /* Easily reachable area */
}
```

### Navigation Patterns

#### Bottom Navigation (Primary)
```typescript
// Optimized for one-handed use
const BottomNavigation = () => (
  <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 safe-area">
    <div className="flex justify-around py-2">
      <NavItem icon="home" label="Home" />
      <NavItem icon="camera" label="Post" />
      <NavItem icon="grid" label="Board" />
      <NavItem icon="history" label="History" />
    </div>
  </nav>
);

const NavItem = ({ icon, label }: { icon: string; label: string }) => (
  <button className="flex flex-col items-center py-2 px-3 min-w-[60px] touch-target">
    <Icon name={icon} className="w-6 h-6 mb-1" />
    <span className="text-xs text-gray-600">{label}</span>
  </button>
);
```

#### Header Navigation (Secondary)
```typescript
const Header = ({ title, onBack }: { title: string; onBack?: () => void }) => (
  <header className="sticky top-0 bg-white border-b border-gray-200 z-10 safe-area">
    <div className="flex items-center h-14 px-4">
      {onBack && (
        <button onClick={onBack} className="mr-3 touch-target">
          <ArrowLeftIcon className="w-6 h-6" />
        </button>
      )}
      <h1 className="text-lg font-semibold text-gray-900 truncate">{title}</h1>
    </div>
  </header>
);
```

## Component Design System

### Cards & Containers
```css
/* Post Card - Primary content container */
.post-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
  transition: all 0.2s ease-in-out;
}

.post-card:hover {
  @apply shadow-md transform translate-y-[-1px];
}

/* Image containers - Optimized for X-ray viewing */
.xray-container {
  @apply relative bg-black rounded-lg overflow-hidden;
  aspect-ratio: 4/3; /* Standard X-ray ratio */
}

.xray-image {
  @apply w-full h-full object-contain cursor-zoom-in;
  filter: contrast(1.1) brightness(1.05); /* Enhance medical image clarity */
}

/* Form containers */
.form-section {
  @apply bg-white rounded-lg p-4 shadow-sm border border-gray-200 space-y-4;
}
```

### Interactive Elements
```css
/* Primary buttons */
.btn-primary {
  @apply bg-blue-600 text-white px-6 py-3 rounded-lg font-medium;
  @apply hover:bg-blue-700 active:bg-blue-800;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  @apply disabled:bg-gray-400 disabled:cursor-not-allowed;
  @apply transition-colors duration-200;
  min-height: 44px; /* Touch target */
}

/* Secondary buttons */
.btn-secondary {
  @apply bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium;
  @apply hover:bg-gray-200 active:bg-gray-300;
  @apply focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  min-height: 44px;
}

/* Floating action button */
.fab {
  @apply fixed bottom-6 right-6 w-14 h-14 bg-blue-600 text-white rounded-full;
  @apply shadow-lg hover:shadow-xl active:scale-95;
  @apply flex items-center justify-center;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  z-index: 50;
}
```

### Form Elements
```css
/* Input fields - Medical form optimized */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg;
  @apply focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply text-base; /* Prevent zoom on iOS */
  min-height: 44px;
}

/* Select dropdowns */
.form-select {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg bg-white;
  @apply focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply appearance-none;
  min-height: 44px;
  background-image: url("data:image/svg+xml;charset=UTF-8,..."); /* Custom arrow */
}

/* Radio buttons - Confidence rating */
.confidence-radio {
  @apply flex items-center justify-center w-12 h-12 rounded-full border-2;
  @apply border-gray-300 text-gray-600 cursor-pointer;
  @apply transition-all duration-200;
}

.confidence-radio.selected {
  @apply border-blue-500 bg-blue-50 text-blue-600;
}

/* Checkbox styling */
.form-checkbox {
  @apply w-5 h-5 text-blue-600 border-gray-300 rounded;
  @apply focus:ring-blue-500 focus:ring-offset-0;
}
```

## Content Layout Patterns

### Image Upload Flow
```typescript
const ImageUploadFlow = () => (
  <div className="space-y-6">
    {/* Upload area */}
    <div className="form-section">
      <h3 className="text-lg font-medium mb-4">Upload X-ray Image</h3>
      <ImageDropzone 
        accept="image/*"
        maxSize={10 * 1024 * 1024}
        className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center"
      />
    </div>

    {/* Image preview */}
    {selectedImage && (
      <div className="form-section">
        <h3 className="text-lg font-medium mb-4">Preview</h3>
        <div className="xray-container">
          <img src={selectedImage} alt="Preview" className="xray-image" />
          <ImageControls /> {/* Zoom, rotate, etc. */}
        </div>
      </div>
    )}

    {/* Description */}
    <div className="form-section">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Case Description (Optional)
      </label>
      <textarea 
        className="form-input resize-none"
        rows={3}
        placeholder="Provide any relevant clinical information..."
      />
    </div>
  </div>
);
```

### Voting Interface
```typescript
const VotingInterface = ({ post }: { post: Post }) => (
  <div className="space-y-6">
    {/* X-ray display */}
    <div className="xray-container">
      <img src={post.imageUrl} alt="X-ray" className="xray-image" />
      <ImageZoom /> {/* Pinch-to-zoom overlay */}
    </div>

    {/* Voting form */}
    <div className="form-section">
      <h3 className="text-lg font-medium mb-4">Your Assessment</h3>
      
      {/* Brand selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Implant Brand
        </label>
        <BrandSelector brands={popularBrands} />
      </div>

      {/* Specifications */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Diameter (mm)
          </label>
          <select className="form-select">
            <option>3.0</option>
            <option>3.5</option>
            <option>4.0</option>
            <option>4.5</option>
            <option>5.0</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Length (mm)
          </label>
          <select className="form-select">
            <option>8</option>
            <option>10</option>
            <option>12</option>
            <option>15</option>
          </select>
        </div>
      </div>

      {/* Confidence rating */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Confidence Level
        </label>
        <ConfidenceSlider />
      </div>

      <button className="btn-primary w-full">
        Submit Vote
      </button>
    </div>
  </div>
);
```

### Results Display
```typescript
const ResultsDisplay = ({ results }: { results: VoteResult[] }) => (
  <div className="space-y-4">
    <div className="bg-blue-50 rounded-lg p-4">
      <h3 className="text-lg font-semibold text-blue-900 mb-2">
        Voting Results
      </h3>
      <p className="text-blue-700">
        Based on {results.reduce((sum, r) => sum + r.votes, 0)} expert votes
      </p>
    </div>

    {results.map((result, index) => (
      <div key={index} className="bg-white rounded-lg p-4 border border-gray-200">
        <div className="flex justify-between items-start mb-3">
          <div>
            <h4 className="font-medium text-gray-900">
              {result.brand} {result.model}
            </h4>
            <p className="text-sm text-gray-600">
              {result.diameter}mm × {result.length}mm
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-blue-600">
              {result.percentage}%
            </div>
            <div className="text-xs text-gray-500">
              {result.votes} votes
            </div>
          </div>
        </div>
        
        {/* Progress bar */}
        <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
          <div 
            className="bg-blue-600 h-3 rounded-full transition-all duration-500"
            style={{ width: `${result.percentage}%` }}
          />
        </div>
        
        {/* Confidence indicator */}
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">
            Avg. Confidence: {result.avgConfidence}/5
          </span>
          <div className="flex">
            {Array.from({ length: 5 }, (_, i) => (
              <StarIcon 
                key={i}
                className={`w-4 h-4 ${
                  i < Math.round(result.avgConfidence) 
                    ? 'text-yellow-400' 
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    ))}
  </div>
);
```

## Accessibility Guidelines

### Color & Contrast
```css
/* Ensure WCAG AA compliance */
.high-contrast {
  --contrast-ratio: 4.5; /* Minimum for normal text */
  --large-contrast-ratio: 3.0; /* Minimum for large text */
}

/* Color-blind friendly palette */
.colorblind-safe {
  --safe-red: #d73027;
  --safe-blue: #4575b4;
  --safe-green: #41ab5d;
  --safe-orange: #feb24c;
}

/* Never rely on color alone */
.status-indicator {
  @apply flex items-center space-x-2;
}

.status-indicator::before {
  content: '';
  @apply w-3 h-3 rounded-full;
}

.status-success::before { @apply bg-green-500; }
.status-warning::before { @apply bg-yellow-500; }
.status-error::before { @apply bg-red-500; }
```

### Screen Reader Support
```typescript
// Semantic HTML structure
const PostCard = ({ post }: { post: Post }) => (
  <article 
    className="post-card"
    aria-labelledby={`post-title-${post.id}`}
    aria-describedby={`post-description-${post.id}`}
  >
    <header>
      <h3 id={`post-title-${post.id}`} className="sr-only">
        Dental implant identification request
      </h3>
    </header>
    
    <div className="xray-container">
      <img 
        src={post.imageUrl} 
        alt={`X-ray image showing dental implant case from ${formatDate(post.createdAt)}`}
        role="img"
      />
    </div>
    
    <div className="p-4">
      <p id={`post-description-${post.id}`}>
        {post.description || 'No additional description provided'}
      </p>
      
      <div className="mt-4 flex space-x-2">
        <button 
          className="btn-primary"
          aria-label={`Vote on implant identification for case ${post.id}`}
        >
          Vote
        </button>
        <button 
          className="btn-secondary"
          aria-label={`View voting results for case ${post.id}`}
        >
          Results
        </button>
      </div>
    </div>
  </article>
);

// Loading states
const LoadingSpinner = () => (
  <div 
    className="flex items-center justify-center p-4"
    role="status"
    aria-live="polite"
  >
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
    <span className="sr-only">Loading content...</span>
  </div>
);

// Error states
const ErrorMessage = ({ message }: { message: string }) => (
  <div 
    className="bg-red-50 border border-red-200 rounded-lg p-4"
    role="alert"
    aria-live="assertive"
  >
    <div className="flex">
      <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
      <div className="ml-3">
        <p className="text-sm text-red-800">{message}</p>
      </div>
    </div>
  </div>
);
```

### Keyboard Navigation
```css
/* Focus indicators */
.focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* Skip links */
.skip-link {
  @apply absolute top-0 left-0 bg-blue-600 text-white px-4 py-2 rounded;
  @apply transform -translate-y-full opacity-0;
  @apply focus:translate-y-0 focus:opacity-100;
  @apply transition-all duration-200;
  z-index: 100;
}

/* Tab order management */
.tab-container {
  @apply focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2;
}
```

## Performance Optimization

### Image Optimization
```typescript
// Progressive image loading
const OptimizedImage = ({ 
  src, 
  alt, 
  className,
  quality = 85 
}: ImageProps) => {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);

  return (
    <div className={`relative ${className}`}>
      {!loaded && !error && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
      )}
      
      <img
        src={`${src}?w=800&h=600&q=${quality}&f=webp`}
        alt={alt}
        className={`transition-opacity duration-300 ${
          loaded ? 'opacity-100' : 'opacity-0'
        }`}
        onLoad={() => setLoaded(true)}
        onError={() => setError(true)}
        loading="lazy"
        decoding="async"
      />
      
      {error && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
          <span className="text-gray-500 text-sm">Failed to load image</span>
        </div>
      )}
    </div>
  );
};

// Image compression before upload
const compressImage = async (file: File): Promise<File> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    const img = new Image();
    
    img.onload = () => {
      const maxWidth = 1200;
      const maxHeight = 900;
      
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }
      
      canvas.width = width;
      canvas.height = height;
      
      ctx.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(resolve as any, 'image/jpeg', 0.8);
    };
    
    img.src = URL.createObjectURL(file);
  });
};
```

### Animation Guidelines
```css
/* Respect user preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Smooth micro-interactions */
.smooth-hover {
  @apply transition-all duration-200 ease-out;
}

.smooth-hover:hover {
  @apply transform translate-y-[-1px] shadow-lg;
}

/* Loading animations */
@keyframes skeleton {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton 1.5s infinite;
}
```

## Error Handling & Feedback

### User-Friendly Error Messages
```typescript
const ErrorMessages = {
  // Network errors
  NETWORK_ERROR: "Connection issue. Please check your internet and try again.",
  TIMEOUT_ERROR: "Request timed out. Please try again.",
  
  // Upload errors  
  FILE_TOO_LARGE: "Image is too large. Please choose an image under 10MB.",
  INVALID_FILE_TYPE: "Please select a JPEG or PNG image.",
  UPLOAD_FAILED: "Upload failed. Please try again.",
  
  // Validation errors
  REQUIRED_FIELD: "This field is required.",
  INVALID_FORMAT: "Please enter a valid format.",
  
  // Vote errors
  ALREADY_VOTED: "You've already voted on this case.",
  VOTE_FAILED: "Vote submission failed. Please try again.",
  
  // Authentication errors
  LOGIN_REQUIRED: "Please log in to continue.",
  SESSION_EXPIRED: "Your session has expired. Please log in again.",
  
  // Generic fallback
  UNKNOWN_ERROR: "Something went wrong. Please try again.",
};

const ErrorDisplay = ({ error, onRetry }: { 
  error: string; 
  onRetry?: () => void; 
}) => (
  <div className="bg-red-50 border border-red-200 rounded-lg p-4 m-4">
    <div className="flex items-start">
      <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mt-0.5" />
      <div className="ml-3 flex-1">
        <p className="text-sm text-red-800">{error}</p>
        {onRetry && (
          <button 
            onClick={onRetry}
            className="mt-2 text-sm text-red-600 hover:text-red-500 underline"
          >
            Try Again
          </button>
        )}
      </div>
    </div>
  </div>
);
```

This comprehensive UX/UI guide ensures your Line LIFF implant voting system provides an excellent user experience that's optimized for medical professionals using mobile devices within the Line ecosystem.