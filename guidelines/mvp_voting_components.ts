// ==================== VOTING COMPONENTS ====================

// src/pages/VotingBoard.tsx - Main Voting Interface
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import { apiService } from '../lib/api';
import PostCard from '../components/PostCard';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

const VotingBoard: React.FC = () => {
  const navigate = useNavigate();
  
  const { data: posts, isLoading, error } = useQuery(
    'posts',
    apiService.getPosts,
    { refetchInterval: 30000 } // Refresh every 30 seconds
  );

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <p className="text-red-600">Failed to load posts</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center">
            <button onClick={() => navigate('/')} className="mr-3">
              <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
            </button>
            <h1 className="text-xl font-bold text-gray-900">Voting Board</h1>
          </div>
        </div>
      </div>

      <div className="max-w-md mx-auto px-4 py-6">
        {posts?.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No posts available for voting</p>
            <button
              onClick={() => navigate('/create')}
              className="mt-4 text-blue-600 hover:text-blue-700"
            >
              Create the first post
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {posts?.map((post) => (
              <PostCard
                key={post.id}
                post={post}
                onVote={() => navigate(`/vote/${post.id}`)}
                onViewResults={() => navigate(`/vote/${post.id}?view=results`)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default VotingBoard;

// src/pages/VotePost.tsx - Individual Post Voting
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';
import { apiService } from '../lib/api';
import VoteForm from '../components/VoteForm';
import VoteResults from '../components/VoteResults';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

const VotePost: React.FC = () => {
  const { postId } = useParams<{ postId: string }>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState<'vote' | 'results'>('vote');

  useEffect(() => {
    if (searchParams.get('view') === 'results') {
      setActiveTab('results');
    }
  }, [searchParams]);

  const { data: post, isLoading: postLoading } = useQuery(
    ['post', postId],
    () => apiService.getPost(postId!),
    { enabled: !!postId }
  );

  const { data: hasVoted } = useQuery(
    ['hasVoted', postId],
    () => apiService.hasUserVoted(postId!),
    { enabled: !!postId }
  );

  const { data: voteSummary } = useQuery(
    ['voteSummary', postId],
    () => apiService.getVoteSummary(postId!),
    { enabled: !!postId && activeTab === 'results' }
  );

  const submitVoteMutation = useMutation(
    (voteData: any) => apiService.submitVote({ ...voteData, post_id: postId! }),
    {
      onSuccess: () => {
        toast.success('Vote submitted successfully!');
        queryClient.invalidateQueries(['hasVoted', postId]);
        queryClient.invalidateQueries(['voteSummary', postId]);
        setActiveTab('results');
      },
      onError: (error: any) => {
        toast.error(error.message || 'Failed to submit vote');
      },
    }
  );

  if (postLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <p className="text-red-600">Post not found</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center">
            <button onClick={() => navigate('/board')} className="mr-3">
              <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
            </button>
            <h1 className="text-xl font-bold text-gray-900">
              {activeTab === 'vote' ? 'Submit Vote' : 'Vote Results'}
            </h1>
          </div>
        </div>
      </div>

      <div className="max-w-md mx-auto px-4 py-6">
        {/* Post Image */}
        <div className="mb-6">
          <img
            src={post.image_url}
            alt="X-ray"
            className="w-full h-64 object-cover rounded-lg border cursor-pointer"
            onClick={() => window.open(post.image_url, '_blank')}
          />
          {post.description && (
            <p className="mt-3 text-gray-700">{post.description}</p>
          )}
        </div>

        {/* Tabs */}
        <div className="flex mb-6 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('vote')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'vote'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
            disabled={hasVoted && activeTab !== 'vote'}
          >
            {hasVoted ? 'Your Vote' : 'Vote'}
          </button>
          <button
            onClick={() => setActiveTab('results')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'results'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Results
          </button>
        </div>

        {/* Content */}
        {activeTab === 'vote' ? (
          hasVoted ? (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <p className="text-green-800 font-medium">✓ You have already voted on this post</p>
              <p className="text-green-600 text-sm mt-1">
                Switch to Results tab to see current voting status
              </p>
            </div>
          ) : (
            <VoteForm
              onSubmit={(data) => submitVoteMutation.mutate(data)}
              isLoading={submitVoteMutation.isLoading}
            />
          )
        ) : (
          <VoteResults summary={voteSummary || []} />
        )}
      </div>
    </div>
  );
};

export default VotePost;

// src/components/VoteForm.tsx - Voting Form Component
import React, { useState } from 'react';

interface VoteFormProps {
  onSubmit: (data: VoteFormData) => void;
  isLoading?: boolean;
}

interface VoteFormData {
  brand: string;
  model: string;
  diameter: string;
  length: string;
  confidence: number;
}

const VoteForm: React.FC<VoteFormProps> = ({ onSubmit, isLoading = false }) => {
  const [formData, setFormData] = useState<VoteFormData>({
    brand: '',
    model: '',
    diameter: '',
    length: '',
    confidence: 5,
  });

  const [errors, setErrors] = useState<Partial<VoteFormData>>({});

  const brands = [
    'Nobel Biocare', 'Straumann', 'Zimmer', 'Dentsply Sirona', 
    'Osstem', 'Hiossen', 'Megagen', 'Other'
  ];

  const diameters = ['3.0', '3.3', '3.5', '4.0', '4.3', '4.5', '5.0', '6.0'];
  const lengths = ['6', '8', '10', '11.5', '13', '15', '16', '18'];

  const validate = () => {
    const newErrors: Partial<VoteFormData> = {};
    
    if (!formData.brand) newErrors.brand = 'Brand is required';
    if (!formData.model) newErrors.model = 'Model is required';
    if (!formData.diameter) newErrors.diameter = 'Diameter is required';
    if (!formData.length) newErrors.length = 'Length is required';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validate()) {
      onSubmit(formData);
    }
  };

  const updateField = (field: keyof VoteFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Brand */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Implant Brand *
        </label>
        <select
          value={formData.brand}
          onChange={(e) => updateField('brand', e.target.value)}
          className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
            errors.brand ? 'border-red-300' : 'border-gray-300'
          }`}
        >
          <option value="">Select brand</option>
          {brands.map(brand => (
            <option key={brand} value={brand}>{brand}</option>
          ))}
        </select>
        {errors.brand && <p className="text-red-500 text-xs mt-1">{errors.brand}</p>}
      </div>

      {/* Model */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Model *
        </label>
        <input
          type="text"
          value={formData.model}
          onChange={(e) => updateField('model', e.target.value)}
          placeholder="e.g., Active, Replace Select, TSV"
          className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
            errors.model ? 'border-red-300' : 'border-gray-300'
          }`}
        />
        {errors.model && <p className="text-red-500 text-xs mt-1">{errors.model}</p>}
      </div>

      {/* Diameter and Length */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Diameter (mm) *
          </label>
          <select
            value={formData.diameter}
            onChange={(e) => updateField('diameter', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.diameter ? 'border-red-300' : 'border-gray-300'
            }`}
          >
            <option value="">Select</option>
            {diameters.map(d => (
              <option key={d} value={d}>{d}mm</option>
            ))}
          </select>
          {errors.diameter && <p className="text-red-500 text-xs mt-1">{errors.diameter}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Length (mm) *
          </label>
          <select
            value={formData.length}
            onChange={(e) => updateField('length', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.length ? 'border-red-300' : 'border-gray-300'
            }`}
          >
            <option value="">Select</option>
            {lengths.map(l => (
              <option key={l} value={l}>{l}mm</option>
            ))}
          </select>
          {errors.length && <p className="text-red-500 text-xs mt-1">{errors.length}</p>}
        </div>
      </div>

      {/* Confidence Level */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Confidence Level: {formData.confidence}/5
        </label>
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-500">Low</span>
          <input
            type="range"
            min="1"
            max="5"
            value={formData.confidence}
            onChange={(e) => updateField('confidence', parseInt(e.target.value))}
            className="flex-1 mx-3"
          />
          <span className="text-xs text-gray-500">High</span>
        </div>
        <div className="flex justify-between text-xs text-gray-400 mt-1">
          <span>1</span><span>2</span><span>3</span><span>4</span><span>5</span>
        </div>
      </div>

      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
      >
        {isLoading ? 'Submitting...' : 'Submit Vote'}
      </button>
    </form>
  );
};

export default VoteForm;

// src/components/VoteResults.tsx - Results Display
import React from 'react';
import { VoteSummary } from '../lib/supabase';

interface VoteResultsProps {
  summary: VoteSummary[];
}

const VoteResults: React.FC<VoteResultsProps> = ({ summary }) => {
  if (!summary || summary.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No votes yet</p>
        <p className="text-sm text-gray-400 mt-1">Be the first to vote!</p>
      </div>
    );
  }

  const totalVotes = summary.reduce((sum, item) => sum + item.vote_count, 0);

  return (
    <div className="space-y-4">
      <div className="bg-blue-50 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-2">Voting Summary</h3>
        <p className="text-sm text-blue-700">
          Total votes: <span className="font-medium">{totalVotes}</span>
        </p>
      </div>

      <div className="space-y-3">
        {summary.map((item, index) => (
          <div key={index} className="bg-white border rounded-lg p-4">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h4 className="font-medium text-gray-900">
                  {item.brand} {item.model}
                </h4>
                <p className="text-sm text-gray-600">
                  {item.diameter}mm × {item.length}mm
                </p>
              </div>
              <div className="text-right">
                <span className="text-lg font-bold text-blue-600">
                  {item.percentage}%
                </span>
                <p className="text-xs text-gray-500">
                  {item.vote_count} vote{item.vote_count !== 1 ? 's' : ''}
                </p>
              </div>
            </div>
            
            {/* Progress bar */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${item.percentage}%` }}
              ></div>
            </div>
            
            <div className="flex justify-between text-xs text-gray-500">
              <span>Confidence: {item.avg_confidence}/5</span>
              <span>
                {Array.from({ length: Math.round(item.avg_confidence) }, (_, i) => '⭐').join('')}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default VoteResults;

// src/pages/MyPosts.tsx - User's Posts History
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import { apiService } from '../lib/api';
import PostCard from '../components/PostCard';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

const MyPosts: React.FC = () => {
  const navigate = useNavigate();
  
  const { data: posts, isLoading, error } = useQuery(
    'myPosts',
    apiService.getMyPosts
  );

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center">
            <button onClick={() => navigate('/')} className="mr-3">
              <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
            </button>
            <h1 className="text-xl font-bold text-gray-900">My Posts</h1>
          </div>
        </div>
      </div>

      <div className="max-w-md mx-auto px-4 py-6">
        {posts?.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">You haven't created any posts yet</p>
            <button
              onClick={() => navigate('/create')}
              className="mt-4 text-blue-600 hover:text-blue-700"
            >
              Create your first post
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {posts?.map((post) => (
              <PostCard
                key={post.id}
                post={post}
                onViewResults={() => navigate(`/vote/${post.id}?view=results`)}
                showActions={true}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MyPosts;

// ==================== SETUP SCRIPTS ====================

// scripts/setup-supabase.js - Automated Supabase Setup
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

async function setupSupabase() {
  console.log('🚀 Setting up Supabase...');
  
  // Read environment variables
  const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
  const supabaseKey = process.env.REACT_APP_SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase credentials in .env.local');
    console.log('Please add:');
    console.log('REACT_APP_SUPABASE_URL=your_supabase_url');
    console.log('REACT_APP_SUPABASE_SERVICE_ROLE_KEY=your_service_role_key');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // Create storage bucket
    console.log('📦 Creating storage bucket...');
    const { error: bucketError } = await supabase.storage.createBucket('xray-images', {
      public: true,
      fileSizeLimit: 10 * 1024 * 1024, // 10MB
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/jpg']
    });

    if (bucketError && !bucketError.message.includes('already exists')) {
      throw bucketError;
    }

    // Set bucket policy
    console.log('🔒 Setting bucket policies...');
    // This would typically be done via SQL in Supabase dashboard

    console.log('✅ Supabase setup complete!');
  } catch (error) {
    console.error('❌ Supabase setup failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  setupSupabase();
}

module.exports = { setupSupabase };

// scripts/setup-line.js - Line Integration Setup Helper
const fs = require('fs');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise(resolve => {
    rl.question(question, resolve);
  });
}

async function setupLine() {
  console.log('🔗 Setting up Line integration...');
  console.log('Please have your Line Developer Console open: https://developers.line.biz/');
  
  try {
    const liffId = await askQuestion('Enter your LIFF ID: ');
    const channelId = await askQuestion('Enter your Line Channel ID: ');
    
    // Read current .env.local
    const envPath = path.join(process.cwd(), '.env.local');
    let envContent = '';
    
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');
    }
    
    // Update or add Line configuration
    const updates = {
      'REACT_APP_LIFF_ID': liffId,
      'REACT_APP_LINE_CHANNEL_ID': channelId
    };
    
    Object.entries(updates).forEach(([key, value]) => {
      const regex = new RegExp(`^${key}=.*$`, 'm');
      if (envContent.match(regex)) {
        envContent = envContent.replace(regex, `${key}=${value}`);
      } else {
        envContent += `\n${key}=${value}`;
      }
    });
    
    fs.writeFileSync(envPath, envContent);
    
    console.log('✅ Line configuration updated in .env.local');
    console.log('📝 Next steps:');
    console.log('1. Update your LIFF app endpoint URL to your deployed app URL');
    console.log('2. Configure webhook URL in Line Console (optional for MVP)');
    console.log('3. Test the integration with: npm run test:liff');
    
  } catch (error) {
    console.error('❌ Line setup failed:', error.message);
  } finally {
    rl.close();
  }
}

if (require.main === module) {
  setupLine();
}

module.exports = { setupLine };