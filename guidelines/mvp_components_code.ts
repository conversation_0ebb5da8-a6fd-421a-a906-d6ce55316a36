// ==================== QUICK SETUP COMPONENTS ====================

// src/App.tsx - Main Application Entry Point
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { AuthProvider } from './contexts/AuthContext';
import { Toaster } from 'react-hot-toast';
import Home from './pages/Home';
import CreatePost from './pages/CreatePost';
import VotingBoard from './pages/VotingBoard';
import MyPosts from './pages/MyPosts';
import VotePost from './pages/VotePost';
import './App.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: { retry: 1, refetchOnWindowFocus: false },
    mutations: { retry: 1 }
  }
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/create" element={<CreatePost />} />
              <Route path="/board" element={<VotingBoard />} />
              <Route path="/my-posts" element={<MyPosts />} />
              <Route path="/vote/:postId" element={<VotePost />} />
            </Routes>
            <Toaster position="top-center" />
          </div>
        </Router>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;

// ==================== AUTHENTICATION CONTEXT ====================

// src/contexts/AuthContext.tsx - Simple Auth Management
import React, { createContext, useContext, useState, useEffect } from 'react';
import liff from '@line/liff';

interface User {
  userId: string;
  displayName: string;
  pictureUrl?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: () => void;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initLiff = async () => {
      try {
        await liff.init({ liffId: process.env.REACT_APP_LIFF_ID! });
        
        if (liff.isLoggedIn()) {
          const profile = await liff.getProfile();
          setUser(profile);
        }
      } catch (error) {
        console.error('LIFF init failed:', error);
      } finally {
        setLoading(false);
      }
    };

    initLiff();
  }, []);

  const login = () => {
    if (!liff.isLoggedIn()) {
      liff.login();
    }
  };

  const logout = () => {
    liff.logout();
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

// ==================== SUPABASE CLIENT ====================

// src/lib/supabase.ts - Database Client
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL!;
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types
export interface Post {
  id: string;
  user_id: string;
  image_url: string;
  description?: string;
  status: string;
  created_at: string;
  expires_at: string;
}

export interface Vote {
  id: string;
  post_id: string;
  user_id: string;
  brand: string;
  model: string;
  diameter: string;
  length: string;
  confidence: number;
  created_at: string;
}

export interface VoteSummary {
  post_id: string;
  brand: string;
  model: string;
  diameter: string;
  length: string;
  vote_count: number;
  avg_confidence: number;
  percentage: number;
}

// ==================== API FUNCTIONS ====================

// src/lib/api.ts - API Functions
import { supabase, Post, Vote } from './supabase';
import { useAuth } from '../contexts/AuthContext';

export const apiService = {
  // Posts
  async createPost(imageFile: File, description?: string): Promise<Post> {
    // Upload image first
    const fileExt = imageFile.name.split('.').pop();
    const fileName = `${Date.now()}.${fileExt}`;
    
    const { data: fileData, error: fileError } = await supabase.storage
      .from('xray-images')
      .upload(fileName, imageFile);

    if (fileError) throw fileError;

    const { data: { publicUrl } } = supabase.storage
      .from('xray-images')
      .getPublicUrl(fileName);

    // Create post record
    const { data, error } = await supabase
      .from('posts')
      .insert({
        image_url: publicUrl,
        description,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async getPosts(): Promise<Post[]> {
    const { data, error } = await supabase
      .from('posts')
      .select('*')
      .eq('status', 'active')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  async getPost(id: string): Promise<Post> {
    const { data, error } = await supabase
      .from('posts')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  async getMyPosts(): Promise<Post[]> {
    const { data, error } = await supabase
      .from('posts')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Votes
  async submitVote(vote: Omit<Vote, 'id' | 'created_at'>): Promise<Vote> {
    const { data, error } = await supabase
      .from('votes')
      .insert(vote)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async getVotes(postId: string): Promise<Vote[]> {
    const { data, error } = await supabase
      .from('votes')
      .select('*')
      .eq('post_id', postId);

    if (error) throw error;
    return data || [];
  },

  async getVoteSummary(postId: string) {
    const { data, error } = await supabase
      .from('vote_summaries')
      .select('*')
      .eq('post_id', postId)
      .order('vote_count', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  async hasUserVoted(postId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('votes')
      .select('id')
      .eq('post_id', postId)
      .limit(1);

    if (error) throw error;
    return (data?.length || 0) > 0;
  }
};

// ==================== REUSABLE COMPONENTS ====================

// src/components/ImageUpload.tsx - Drag & Drop Upload
import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { PhotoIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface ImageUploadProps {
  onImageSelect: (file: File) => void;
  selectedImage?: File;
  onImageRemove?: () => void;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageSelect,
  selectedImage,
  onImageRemove,
}) => {
  const [preview, setPreview] = useState<string>('');

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      onImageSelect(file);
      const reader = new FileReader();
      reader.onload = () => setPreview(reader.result as string);
      reader.readAsDataURL(file);
    }
  }, [onImageSelect]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { 'image/*': ['.jpeg', '.jpg', '.png'] },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: false,
  });

  const handleRemove = () => {
    setPreview('');
    onImageRemove?.();
  };

  if (preview) {
    return (
      <div className="relative">
        <img
          src={preview}
          alt="Preview"
          className="w-full h-64 object-cover rounded-lg border-2 border-gray-300"
        />
        <button
          onClick={handleRemove}
          className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
        >
          <XMarkIcon className="w-5 h-5" />
        </button>
      </div>
    );
  }

  return (
    <div
      {...getRootProps()}
      className={`w-full h-64 border-2 border-dashed rounded-lg flex flex-col items-center justify-center cursor-pointer transition-colors
        ${isDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}`}
    >
      <input {...getInputProps()} />
      <PhotoIcon className="w-12 h-12 text-gray-400 mb-4" />
      <p className="text-gray-600 text-center">
        {isDragActive ? (
          'Drop the X-ray image here...'
        ) : (
          <>
            <span className="font-medium">Click to upload</span> or drag and drop
            <br />
            <span className="text-sm">PNG, JPG up to 10MB</span>
          </>
        )}
      </p>
    </div>
  );
};

export default ImageUpload;

// src/components/PostCard.tsx - Post Display Component
import React from 'react';
import { Post } from '../lib/supabase';
import { ClockIcon, UserIcon } from '@heroicons/react/24/outline';

interface PostCardProps {
  post: Post;
  onVote?: () => void;
  onViewResults?: () => void;
  showActions?: boolean;
}

const PostCard: React.FC<PostCardProps> = ({
  post,
  onVote,
  onViewResults,
  showActions = true,
}) => {
  const timeAgo = (date: string) => {
    const now = new Date();
    const posted = new Date(date);
    const diffMs = now.getTime() - posted.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    
    if (diffHours < 1) return 'Just now';
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${Math.floor(diffHours / 24)}d ago`;
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <img
        src={post.image_url}
        alt="X-ray"
        className="w-full h-48 object-cover cursor-pointer"
        onClick={() => window.open(post.image_url, '_blank')}
      />
      
      <div className="p-4">
        {post.description && (
          <p className="text-gray-700 mb-3">{post.description}</p>
        )}
        
        <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
          <div className="flex items-center">
            <UserIcon className="w-4 h-4 mr-1" />
            <span>Anonymous</span>
          </div>
          <div className="flex items-center">
            <ClockIcon className="w-4 h-4 mr-1" />
            <span>{timeAgo(post.created_at)}</span>
          </div>
        </div>

        {showActions && (
          <div className="flex space-x-2">
            <button
              onClick={onVote}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              Vote
            </button>
            <button
              onClick={onViewResults}
              className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors"
            >
              Results
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default PostCard;

// ==================== PAGE COMPONENTS ====================

// src/pages/Home.tsx - Landing Page
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { PlusIcon, ViewfinderCircleIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { user, loading, login } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center p-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🦷 Implant ID Voting
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Get expert opinions on dental implant identification
          </p>
          <button
            onClick={login}
            className="bg-green-500 text-white px-8 py-3 rounded-lg text-lg font-medium hover:bg-green-600 transition-colors"
          >
            Login with LINE
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm">
        <div className="max-w-md mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold text-gray-900">Implant Voting</h1>
            <img
              src={user.pictureUrl}
              alt={user.displayName}
              className="w-8 h-8 rounded-full"
            />
          </div>
        </div>
      </div>

      <div className="max-w-md mx-auto px-4 py-6">
        <div className="grid grid-cols-1 gap-4">
          <button
            onClick={() => navigate('/create')}
            className="bg-white p-6 rounded-lg shadow-md flex items-center space-x-4 hover:shadow-lg transition-shadow"
          >
            <div className="bg-blue-100 p-3 rounded-full">
              <PlusIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div className="text-left">
              <h2 className="font-semibold text-gray-900">Create Post</h2>
              <p className="text-gray-600 text-sm">Upload X-ray for identification</p>
            </div>
          </button>

          <button
            onClick={() => navigate('/board')}
            className="bg-white p-6 rounded-lg shadow-md flex items-center space-x-4 hover:shadow-lg transition-shadow"
          >
            <div className="bg-green-100 p-3 rounded-full">
              <ViewfinderCircleIcon className="w-6 h-6 text-green-600" />
            </div>
            <div className="text-left">
              <h2 className="font-semibold text-gray-900">Voting Board</h2>
              <p className="text-gray-600 text-sm">Help identify implants</p>
            </div>
          </button>

          <button
            onClick={() => navigate('/my-posts')}
            className="bg-white p-6 rounded-lg shadow-md flex items-center space-x-4 hover:shadow-lg transition-shadow"
          >
            <div className="bg-purple-100 p-3 rounded-full">
              <DocumentTextIcon className="w-6 h-6 text-purple-600" />
            </div>
            <div className="text-left">
              <h2 className="font-semibold text-gray-900">My Posts</h2>
              <p className="text-gray-600 text-sm">View your submissions</p>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Home;

// src/pages/CreatePost.tsx - Post Creation Page
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation } from 'react-query';
import toast from 'react-hot-toast';
import { apiService } from '../lib/api';
import ImageUpload from '../components/ImageUpload';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

const CreatePost: React.FC = () => {
  const navigate = useNavigate();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [description, setDescription] = useState('');

  const createPostMutation = useMutation(
    ({ file, desc }: { file: File; desc: string }) =>
      apiService.createPost(file, desc),
    {
      onSuccess: () => {
        toast.success('Post created successfully!');
        navigate('/board');
      },
      onError: (error: any) => {
        toast.error(error.message || 'Failed to create post');
      },
    }
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedFile) {
      toast.error('Please select an image');
      return;
    }
    createPostMutation.mutate({ file: selectedFile, desc: description });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center">
            <button onClick={() => navigate('/')} className="mr-3">
              <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
            </button>
            <h1 className="text-xl font-bold text-gray-900">Create Post</h1>
          </div>
        </div>
      </div>

      <div className="max-w-md mx-auto px-4 py-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              X-ray Image *
            </label>
            <ImageUpload
              onImageSelect={setSelectedFile}
              selectedImage={selectedFile || undefined}
              onImageRemove={() => setSelectedFile(null)}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description (Optional)
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              placeholder="Add any relevant details about the case..."
              maxLength={500}
            />
            <p className="text-xs text-gray-500 mt-1">
              {description.length}/500 characters
            </p>
          </div>

          <button
            type="submit"
            disabled={!selectedFile || createPostMutation.isLoading}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {createPostMutation.isLoading ? 'Creating...' : 'Create Post'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default CreatePost;