# MVP Quick Setup & Launch Guide

## 🚀 Rapid Deployment Strategy (48-Hour Launch)

### Phase 1: Foundation (8 hours)
1. **Hour 1-2**: Supabase project setup + database schema
2. **Hour 3-4**: Line developer account + LIFF app creation
3. **Hour 5-6**: React project initialization + basic routing
4. **Hour 7-8**: Authentication integration + basic UI

### Phase 2: Core Features (24 hours)
1. **Hours 9-16**: Image upload + post creation
2. **Hours 17-24**: Voting system + basic results
3. **Hours 25-32**: Notifications + Line integration

### Phase 3: Polish & Deploy (16 hours)
1. **Hours 33-40**: UI/UX improvements + mobile optimization
2. **Hours 41-48**: Testing + deployment + go-live

## 🛠 Quick Setup Commands

### 1. Initialize Project (5 minutes)
```bash
# Clone starter template
git clone https://github.com/your-org/liff-voting-starter.git implant-voting
cd implant-voting

# Install dependencies
npm install

# Copy environment template
cp .env.example .env.local

# Start development server
npm start
```

### 2. Supabase Setup (10 minutes)
```bash
# Install Supabase CLI
npm install -g supabase

# Login and create project
supabase login
supabase init
supabase start

# Run quick setup script
npm run setup:database
```

### 3. Line Integration Setup (15 minutes)
```bash
# Configure Line credentials
npm run setup:line

# Test LIFF integration
npm run test:liff
```

## 📁 MVP Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── common/
│   │   ├── Button.tsx
│   │   ├── Modal.tsx
│   │   └── LoadingSpinner.tsx
│   ├── ImageUpload.tsx   # Drag & drop image upload
│   ├── PostCard.tsx      # Post display component
│   ├── VoteForm.tsx      # Simple voting form
│   └── VoteResults.tsx   # Results visualization
├── pages/                # Main application pages
│   ├── Home.tsx          # Landing page
│   ├── CreatePost.tsx    # Post creation
│   ├── VotingBoard.tsx   # Main voting interface
│   └── MyPosts.tsx       # User's posts history
├── services/             # API and external services
│   ├── supabase.ts       # Database client
│   ├── liff.ts           # Line integration
│   ├── storage.ts        # Image storage
│   └── api.ts            # API client
├── hooks/                # Custom React hooks
│   ├── useAuth.ts        # Authentication
│   ├── usePosts.ts       # Posts management
│   └── useVotes.ts       # Voting functionality
├── utils/                # Helper functions
│   ├── constants.ts      # App constants
│   ├── validation.ts     # Form validation
│   └── helpers.ts        # Utility functions
└── types/                # TypeScript definitions
    ├── database.ts       # Supabase types
    ├── liff.ts          # Line types
    └── app.ts           # App-specific types
```

## ⚡ MVP Database Schema (Minimal)

```sql
-- Run this in Supabase SQL editor for instant setup

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (simplified)
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  line_user_id TEXT UNIQUE NOT NULL,
  display_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Posts table (essential fields only)
CREATE TABLE posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  image_url TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'active',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '24 hours')
);

-- Votes table (simplified)
CREATE TABLE votes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id UUID REFERENCES posts(id),
  user_id UUID REFERENCES users(id),
  brand TEXT NOT NULL,
  model TEXT NOT NULL,
  diameter TEXT NOT NULL,
  length TEXT NOT NULL,
  confidence INTEGER CHECK (confidence >= 1 AND confidence <= 5),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(post_id, user_id)
);

-- Vote summaries view (auto-calculated)
CREATE OR REPLACE VIEW vote_summaries AS
SELECT 
  post_id,
  brand,
  model,
  diameter,
  length,
  COUNT(*) as vote_count,
  ROUND(AVG(confidence), 1) as avg_confidence,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (PARTITION BY post_id), 1) as percentage
FROM votes
GROUP BY post_id, brand, model, diameter, length;

-- Enable RLS (basic security)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE votes ENABLE ROW LEVEL SECURITY;

-- Simple RLS policies
CREATE POLICY "Users can see all posts" ON posts FOR SELECT USING (true);
CREATE POLICY "Users can create posts" ON posts FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
CREATE POLICY "Users can vote" ON votes FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
CREATE POLICY "Users can see votes" ON votes FOR SELECT USING (true);
```

## 🔧 MVP Environment Configuration

### `.env.local` (Development)
```bash
# Supabase
REACT_APP_SUPABASE_URL=your_supabase_project_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key

# Line LIFF
REACT_APP_LIFF_ID=your_liff_app_id

# App Settings
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=image/jpeg,image/png
REACT_APP_POST_EXPIRY_HOURS=24

# Feature Flags (for quick testing)
REACT_APP_ENABLE_NOTIFICATIONS=false
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_DEBUG_MODE=true
```

## 📱 MVP Component Library

### Quick Setup Package.json Scripts
```json
{
  "scripts": {
    "setup:all": "npm run setup:supabase && npm run setup:line && npm run setup:ui",
    "setup:supabase": "node scripts/setup-supabase.js",
    "setup:line": "node scripts/setup-line.js",
    "setup:ui": "npm install @headlessui/react @heroicons/react tailwindcss",
    "dev": "npm start",
    "build:staging": "REACT_APP_ENV=staging npm run build",
    "build:prod": "REACT_APP_ENV=production npm run build",
    "deploy:vercel": "vercel --prod",
    "deploy:netlify": "netlify deploy --prod --dir=build",
    "test:quick": "npm test -- --watchAll=false --coverage=false",
    "db:reset": "supabase db reset",
    "db:migrate": "supabase db push"
  }
}
```

## 🎨 MVP Tailwind Config

```javascript
// tailwind.config.js - Optimized for mobile-first
module.exports = {
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
        success: '#10b981',
        warning: '#f59e0b',
        danger: '#ef4444',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
    },
  },
  plugins: [],
};
```

## 🔥 Fast Development Tips

### 1. Use React Query for Data Management
```bash
npm install react-query
```

### 2. Quick Authentication Hook
```typescript
// hooks/useQuickAuth.ts - Drop-in authentication
import { useState, useEffect } from 'react';
import liff from '@line/liff';

export const useQuickAuth = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        await liff.init({ liffId: process.env.REACT_APP_LIFF_ID! });
        if (liff.isLoggedIn()) {
          const profile = await liff.getProfile();
          setUser(profile);
        }
      } catch (error) {
        console.error('Auth error:', error);
      } finally {
        setLoading(false);
      }
    };
    initAuth();
  }, []);

  const login = () => liff.login();
  const logout = () => liff.logout();

  return { user, loading, login, logout, isLoggedIn: !!user };
};
```

### 3. One-Click Deployment Scripts

#### Vercel Deployment
```json
// vercel.json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": { "distDir": "build" }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ],
  "env": {
    "REACT_APP_SUPABASE_URL": "@supabase_url",
    "REACT_APP_SUPABASE_ANON_KEY": "@supabase_anon_key",
    "REACT_APP_LIFF_ID": "@liff_id"
  }
}
```

#### Netlify Deployment
```toml
# netlify.toml
[build]
  publish = "build"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  REACT_APP_SUPABASE_URL = "your_supabase_url"
  REACT_APP_SUPABASE_ANON_KEY = "your_supabase_anon_key"
  REACT_APP_LIFF_ID = "your_liff_id"
```

## 🎯 MVP Feature Checklist

### Core Features (Must-Have)
- [ ] Line LIFF authentication
- [ ] Image upload (drag & drop)
- [ ] Create post with description
- [ ] View posts on voting board
- [ ] Submit votes (brand, model, size, confidence)
- [ ] View basic vote results
- [ ] Mobile-responsive design

### Enhanced Features (Nice-to-Have)
- [ ] Real-time vote updates
- [ ] Post expiration (24 hours)
- [ ] User post history
- [ ] Vote notifications
- [ ] Search/filter posts
- [ ] Image zoom/pan
- [ ] Vote confidence visualization

### Future Features (Post-MVP)
- [ ] AI-powered suggestions
- [ ] Expert verification
- [ ] Advanced analytics
- [ ] Multi-language support
- [ ] Export functionality

## 🚦 Go-Live Checklist

### Pre-Launch (Day 1)
- [ ] Test all features on mobile
- [ ] Verify Line LIFF integration
- [ ] Check image upload/storage
- [ ] Test voting flow end-to-end
- [ ] Performance optimization
- [ ] Basic error handling

### Launch Day (Day 2)
- [ ] Deploy to production
- [ ] Update Line LIFF endpoint URL
- [ ] Configure monitoring
- [ ] Prepare support documentation
- [ ] Announce to test users
- [ ] Monitor system performance

### Post-Launch (Day 3+)
- [ ] Collect user feedback
- [ ] Monitor error rates
- [ ] Track user engagement
- [ ] Plan feature iterations
- [ ] Scale based on usage

## 📊 Success Metrics for MVP

### Technical Metrics
- **Page Load Time**: < 3 seconds
- **Image Upload Success**: > 95%
- **Vote Submission Success**: > 98%
- **Mobile Usability**: 100% responsive
- **Uptime**: > 99%

### Business Metrics
- **User Registration**: 50+ users in first week
- **Post Creation**: 10+ posts in first week
- **Vote Participation**: Average 5+ votes per post
- **User Retention**: 70% return within 3 days
- **Expert Engagement**: 30% of votes from verified doctors

This MVP approach gets you from idea to live application in 48 hours with a focus on core functionality and rapid iteration based on user feedback.