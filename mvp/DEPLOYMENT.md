# 🚀 Deployment Guide

This guide covers deploying the Implant Voting System MVP to various platforms.

## 📋 Pre-Deployment Checklist

### Environment Setup
- [ ] `.env` file configured with all required variables
- [ ] Supabase project created and configured
- [ ] LINE LIFF app created and configured
- [ ] Domain/hosting platform selected
- [ ] SSL certificate configured (HTTPS required for LIFF)

### Code Quality
- [ ] All tests passing (`npm test`)
- [ ] No security vulnerabilities (`npm audit`)
- [ ] Code linted and formatted
- [ ] Build successful (`npm run build`)

### Database
- [ ] Supabase migrations applied
- [ ] RLS policies configured
- [ ] Storage bucket created and configured
- [ ] Database indexes optimized

## 🌐 Deployment Platforms

### Vercel (Recommended)

#### Quick Deploy
```bash
npm install -g vercel
vercel login
npm run deploy:vercel
```

#### Manual Setup
1. Connect GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Set build command: `npm run build:prod`
4. Set output directory: `build`
5. Deploy

#### Environment Variables
```
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
REACT_APP_LIFF_ID=your_liff_id
REACT_APP_LINE_CHANNEL_ID=your_line_channel_id
REACT_APP_ENVIRONMENT=production
```

### Netlify

#### Quick Deploy
```bash
npm install -g netlify-cli
netlify login
npm run deploy:netlify
```

#### Manual Setup
1. Connect GitHub repository to Netlify
2. Configure build settings:
   - Build command: `npm run build:prod`
   - Publish directory: `build`
3. Set environment variables
4. Deploy

### Docker Deployment

#### Build Image
```bash
docker build -t implant-voting-mvp .
```

#### Run Container
```bash
docker run -p 80:80 \
  -e REACT_APP_SUPABASE_URL=your_url \
  -e REACT_APP_SUPABASE_ANON_KEY=your_key \
  -e REACT_APP_LIFF_ID=your_liff_id \
  -e REACT_APP_LINE_CHANNEL_ID=your_channel_id \
  implant-voting-mvp
```

#### Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "80:80"
    environment:
      - REACT_APP_SUPABASE_URL=${REACT_APP_SUPABASE_URL}
      - REACT_APP_SUPABASE_ANON_KEY=${REACT_APP_SUPABASE_ANON_KEY}
      - REACT_APP_LIFF_ID=${REACT_APP_LIFF_ID}
      - REACT_APP_LINE_CHANNEL_ID=${REACT_APP_LINE_CHANNEL_ID}
    restart: unless-stopped
```

## 🔧 Configuration

### Environment Variables

#### Required
- `REACT_APP_SUPABASE_URL` - Your Supabase project URL
- `REACT_APP_SUPABASE_ANON_KEY` - Supabase anonymous key
- `REACT_APP_LIFF_ID` - LINE LIFF application ID
- `REACT_APP_LINE_CHANNEL_ID` - LINE channel ID

#### Optional
- `REACT_APP_MAX_FILE_SIZE` - Maximum upload size (default: 10MB)
- `REACT_APP_POST_EXPIRY_HOURS` - Post expiry time (default: 24)
- `REACT_APP_ENVIRONMENT` - Environment name
- `REACT_APP_DEBUG_MODE` - Enable debug logging

### Security Headers

The application includes security headers configured in:
- `vercel.json` for Vercel deployments
- `netlify.toml` for Netlify deployments
- `nginx.conf` for Docker/custom deployments

Key security headers:
- Content Security Policy (CSP)
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- X-XSS-Protection
- Referrer-Policy

## 📱 LINE LIFF Configuration

### Update Endpoint URL
After deployment, update your LIFF app endpoint URL:

1. Go to LINE Developers Console
2. Select your channel
3. Go to LIFF tab
4. Edit your LIFF app
5. Update Endpoint URL to your deployed domain
6. Save changes

### Testing LIFF Integration
1. Use LIFF Inspector: https://liff-inspector.line.me/
2. Enter your LIFF URL
3. Test on actual mobile devices
4. Verify authentication flow

## 🔍 Post-Deployment Verification

### Automated Checks
```bash
# Run deployment script with checks
./scripts/deploy.sh production

# Manual health check
curl -f https://your-domain.com/health
```

### Manual Testing Checklist
- [ ] Application loads correctly
- [ ] LINE LIFF authentication works
- [ ] Image upload functionality
- [ ] Voting system works
- [ ] Results display correctly
- [ ] Mobile responsiveness
- [ ] Security headers present

### Performance Testing
- [ ] Page load times < 3 seconds
- [ ] Image upload works smoothly
- [ ] No console errors
- [ ] Responsive on various devices

## 📊 Monitoring & Analytics

### Error Tracking
- Set up error monitoring (Sentry, LogRocket, etc.)
- Monitor Supabase logs
- Check browser console for errors

### Performance Monitoring
- Use Lighthouse for performance audits
- Monitor Core Web Vitals
- Track user engagement metrics

### Security Monitoring
- Regular security audits
- Monitor for suspicious activity
- Keep dependencies updated

## 🔄 Continuous Deployment

### GitHub Actions Example
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm test
      - run: npm run build:prod
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

## 🆘 Troubleshooting

### Common Issues

#### LIFF Not Loading
- Check HTTPS is enabled
- Verify LIFF endpoint URL
- Check CSP headers allow LINE domains

#### Authentication Errors
- Verify Supabase configuration
- Check LINE channel settings
- Ensure environment variables are set

#### Image Upload Failures
- Check Supabase storage configuration
- Verify file size limits
- Check network connectivity

#### Build Failures
- Clear node_modules and reinstall
- Check for TypeScript errors
- Verify environment variables

### Getting Help
1. Check application logs
2. Review browser console
3. Check Supabase dashboard
4. Verify LINE LIFF settings
5. Contact support team

## 📚 Additional Resources

- [Vercel Documentation](https://vercel.com/docs)
- [Netlify Documentation](https://docs.netlify.com/)
- [LINE LIFF Documentation](https://developers.line.biz/en/docs/liff/)
- [Supabase Documentation](https://supabase.com/docs)
- [React Deployment Guide](https://create-react-app.dev/docs/deployment/)

---

**Note:** Always test deployments in a staging environment before deploying to production. Ensure all team members are familiar with the deployment process and have necessary access credentials.
