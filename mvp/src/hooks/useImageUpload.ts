import { useState, useCallback } from 'react';
import { useMutation } from 'react-query';
import toast from 'react-hot-toast';
import { ImageProcessingService } from '../services/imageProcessing';
import { apiService } from '../services/api';
import { SecurityService } from '../utils/security';

interface UseImageUploadOptions {
  onSuccess?: (imageUrl: string) => void;
  onError?: (error: string) => void;
  maxSize?: number;
  allowedTypes?: string[];
}

export const useImageUpload = (options: UseImageUploadOptions = {}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string>('');
  const [thumbnail, setThumbnail] = useState<string>('');
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [processingProgress, setProcessingProgress] = useState(0);

  const {
    onSuccess,
    onError,
    maxSize = 10 * 1024 * 1024, // 10MB
    allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
  } = options;

  // Upload mutation
  const uploadMutation = useMutation(
    async (file: File) => {
      setProcessingProgress(10);
      
      // Rate limiting check
      if (!SecurityService.checkRateLimit('upload_image', 'current_user', 5, 60000)) {
        throw new Error('Upload rate limit exceeded. Please wait before uploading again.');
      }

      setProcessingProgress(20);

      // Process image
      const { processedFile, metadata, thumbnail: thumbUrl } = await ImageProcessingService.processImageForUpload(file);
      
      setProcessingProgress(50);
      setThumbnail(thumbUrl);

      // Upload to storage
      const imageUrl = await apiService.uploadImage(processedFile);
      
      setProcessingProgress(100);

      return { imageUrl, metadata };
    },
    {
      onSuccess: ({ imageUrl }) => {
        toast.success('Image uploaded successfully!');
        onSuccess?.(imageUrl);
        setProcessingProgress(0);
      },
      onError: (error: any) => {
        const errorMessage = error.message || 'Failed to upload image';
        toast.error(errorMessage);
        onError?.(errorMessage);
        setProcessingProgress(0);
      },
    }
  );

  // Validate and select file
  const handleFileSelect = useCallback(async (file: File) => {
    setValidationErrors([]);
    setSelectedFile(null);
    setPreview('');
    setThumbnail('');

    try {
      // Basic validation
      const errors: string[] = [];

      // File type check
      if (!allowedTypes.includes(file.type)) {
        errors.push(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`);
      }

      // File size check
      if (file.size > maxSize) {
        errors.push(`File size too large. Maximum size: ${Math.round(maxSize / 1024 / 1024)}MB`);
      }

      // File name validation
      if (!/^[a-zA-Z0-9._-]+$/.test(file.name)) {
        errors.push('Invalid file name. Only letters, numbers, dots, hyphens, and underscores are allowed.');
      }

      if (errors.length > 0) {
        setValidationErrors(errors);
        return;
      }

      // Advanced validation
      const validation = await ImageProcessingService.validateImageContent(file);
      if (!validation.valid) {
        setValidationErrors(validation.errors);
        return;
      }

      // Medical image validation (warnings only)
      const medicalValidation = await ImageProcessingService.validateMedicalImage(file);
      if (medicalValidation.warnings.length > 0) {
        // Show warnings but don't block upload
        medicalValidation.warnings.forEach(warning => {
          toast(warning, { icon: '⚠️', duration: 5000 });
        });
      }

      // Create preview
      const previewUrl = URL.createObjectURL(file);
      setPreview(previewUrl);
      setSelectedFile(file);

      // Create thumbnail
      try {
        const thumbnailUrl = await ImageProcessingService.createThumbnail(file);
        setThumbnail(thumbnailUrl);
      } catch (error) {
        console.warn('Failed to create thumbnail:', error);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to validate image';
      setValidationErrors([errorMessage]);
    }
  }, [allowedTypes, maxSize]);

  // Upload selected file
  const uploadFile = useCallback(() => {
    if (!selectedFile) {
      toast.error('No file selected');
      return;
    }

    uploadMutation.mutate(selectedFile);
  }, [selectedFile, uploadMutation]);

  // Remove selected file
  const removeFile = useCallback(() => {
    if (preview) {
      URL.revokeObjectURL(preview);
    }
    setSelectedFile(null);
    setPreview('');
    setThumbnail('');
    setValidationErrors([]);
    setProcessingProgress(0);
  }, [preview]);

  // Reset all state
  const reset = useCallback(() => {
    removeFile();
    uploadMutation.reset();
  }, [removeFile, uploadMutation]);

  return {
    // State
    selectedFile,
    preview,
    thumbnail,
    validationErrors,
    processingProgress,
    
    // Status
    isUploading: uploadMutation.isLoading,
    isSuccess: uploadMutation.isSuccess,
    isError: uploadMutation.isError,
    error: uploadMutation.error,
    
    // Actions
    handleFileSelect,
    uploadFile,
    removeFile,
    reset,
    
    // Computed
    hasValidFile: selectedFile && validationErrors.length === 0,
    canUpload: selectedFile && validationErrors.length === 0 && !uploadMutation.isLoading,
  };
};
