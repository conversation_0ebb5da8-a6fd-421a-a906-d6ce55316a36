import { useMemo } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { UserRole, Permission } from '../types/auth';

// Role-based permissions mapping
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.BASIC_USER]: [
    Permission.CREATE_POST,
    Permission.VOTE_ON_POST,
  ],
  [UserRole.VERIFIED_DOCTOR]: [
    Permission.CREATE_POST,
    Permission.VOTE_ON_POST,
    Permission.VIEW_DETAILED_RESULTS,
  ],
  [UserRole.SPECIALIST]: [
    Permission.CREATE_POST,
    Permission.VOTE_ON_POST,
    Permission.VIEW_DETAILED_RESULTS,
  ],
  [UserRole.MODERATOR]: [
    Permission.CREATE_POST,
    Permission.VOTE_ON_POST,
    Permission.VIEW_DETAILED_RESULTS,
    Permission.MODERATE_CONTENT,
    Permission.ACCESS_ANALYTICS,
  ],
  [UserRole.ADMIN]: [
    Permission.CREATE_POST,
    Permission.VOTE_ON_POST,
    Permission.VIEW_DETAILED_RESULTS,
    Permission.MODERATE_CONTENT,
    Permission.ACCESS_ANALYTICS,
    Permission.MANAGE_USERS,
    Permission.SYSTEM_ADMIN,
  ],
};

export const usePermissions = () => {
  const { user } = useAuth();

  const permissions = useMemo(() => {
    if (!user) return [];
    return ROLE_PERMISSIONS[user.role] || [];
  }, [user]);

  const hasPermission = (permission: Permission): boolean => {
    return permissions.includes(permission);
  };

  const hasAnyPermission = (requiredPermissions: Permission[]): boolean => {
    return requiredPermissions.some(permission => hasPermission(permission));
  };

  const hasAllPermissions = (requiredPermissions: Permission[]): boolean => {
    return requiredPermissions.every(permission => hasPermission(permission));
  };

  const canCreatePost = (): boolean => {
    return hasPermission(Permission.CREATE_POST);
  };

  const canVote = (): boolean => {
    return hasPermission(Permission.VOTE_ON_POST);
  };

  const canViewDetailedResults = (): boolean => {
    return hasPermission(Permission.VIEW_DETAILED_RESULTS);
  };

  const canModerateContent = (): boolean => {
    return hasPermission(Permission.MODERATE_CONTENT);
  };

  const canAccessAnalytics = (): boolean => {
    return hasPermission(Permission.ACCESS_ANALYTICS);
  };

  const canManageUsers = (): boolean => {
    return hasPermission(Permission.MANAGE_USERS);
  };

  const isAdmin = (): boolean => {
    return hasPermission(Permission.SYSTEM_ADMIN);
  };

  const isVerifiedProfessional = (): boolean => {
    return user?.role === UserRole.VERIFIED_DOCTOR || 
           user?.role === UserRole.SPECIALIST ||
           user?.role === UserRole.MODERATOR ||
           user?.role === UserRole.ADMIN;
  };

  return {
    permissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canCreatePost,
    canVote,
    canViewDetailedResults,
    canModerateContent,
    canAccessAnalytics,
    canManageUsers,
    isAdmin,
    isVerifiedProfessional,
    userRole: user?.role,
  };
};
