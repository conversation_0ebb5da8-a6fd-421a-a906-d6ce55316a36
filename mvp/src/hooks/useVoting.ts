import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';
import { apiService } from '../services/api';
import { ValidationService } from '../utils/validation';
import { VoteFormData } from '../types/app';

interface UseVotingOptions {
  postId: string;
  onVoteSuccess?: () => void;
  onVoteError?: (error: string) => void;
}

export const useVoting = ({ postId, onVoteSuccess, onVoteError }: UseVotingOptions) => {
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check if user has already voted
  const { data: hasVoted, isLoading: checkingVoteStatus } = useQuery(
    ['hasVoted', postId],
    () => apiService.hasUserVoted(postId),
    {
      enabled: !!postId,
      staleTime: 30000, // Cache for 30 seconds
    }
  );

  // Get vote summary
  const { 
    data: voteSummary, 
    isLoading: loadingSummary,
    refetch: refetchSummary 
  } = useQuery(
    ['voteSummary', postId],
    () => apiService.getVoteSummary(postId),
    {
      enabled: !!postId,
      refetchInterval: 10000, // Refresh every 10 seconds
      staleTime: 5000, // Consider stale after 5 seconds
    }
  );

  // Submit vote mutation
  const submitVoteMutation = useMutation(
    async (voteData: VoteFormData) => {
      setIsSubmitting(true);

      // Validate rate limiting
      if (!ValidationService.validateRateLimit('submit_vote', 'current_user')) {
        throw new Error('You have reached the voting limit. Please try again later.');
      }

      // Validate vote data
      const validation = ValidationService.validateVoteSubmission(voteData);
      if (!validation.valid) {
        throw new Error(validation.errors.join(', '));
      }

      // Submit vote
      return apiService.submitVote({ ...voteData, post_id: postId });
    },
    {
      onSuccess: () => {
        toast.success('Vote submitted successfully!');
        
        // Invalidate related queries
        queryClient.invalidateQueries(['hasVoted', postId]);
        queryClient.invalidateQueries(['voteSummary', postId]);
        queryClient.invalidateQueries(['votes', postId]);
        
        onVoteSuccess?.();
        setIsSubmitting(false);
      },
      onError: (error: any) => {
        const errorMessage = error.message || 'Failed to submit vote';
        toast.error(errorMessage);
        onVoteError?.(errorMessage);
        setIsSubmitting(false);
      },
    }
  );

  // Calculate voting statistics
  const votingStats = {
    totalVotes: voteSummary?.reduce((sum, item) => sum + item.vote_count, 0) || 0,
    uniqueAssessments: voteSummary?.length || 0,
    topChoice: voteSummary?.[0] || null,
    consensusLevel: voteSummary?.[0] ? (voteSummary[0].percentage / 100) : 0,
  };

  // Check if there's a strong consensus (>60% agreement)
  const hasStrongConsensus = votingStats.consensusLevel > 0.6 && votingStats.totalVotes >= 3;

  // Check if voting is still active
  const isVotingActive = !hasVoted && votingStats.totalVotes < 50; // Limit to 50 votes max

  // Get confidence distribution
  const confidenceDistribution = voteSummary?.reduce((acc, item) => {
    const confidence = Math.round(item.avg_confidence);
    acc[confidence] = (acc[confidence] || 0) + item.vote_count;
    return acc;
  }, {} as Record<number, number>) || {};

  return {
    // Vote status
    hasVoted,
    checkingVoteStatus,
    isVotingActive,
    
    // Vote submission
    submitVote: submitVoteMutation.mutate,
    isSubmitting: isSubmitting || submitVoteMutation.isLoading,
    submitError: submitVoteMutation.error,
    
    // Vote results
    voteSummary,
    loadingSummary,
    refetchSummary,
    
    // Statistics
    votingStats,
    hasStrongConsensus,
    confidenceDistribution,
    
    // Computed values
    canVote: !hasVoted && !checkingVoteStatus,
    showResults: hasVoted || votingStats.totalVotes > 0,
  };
};
