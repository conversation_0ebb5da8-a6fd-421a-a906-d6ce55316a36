import React from 'react';
import { StarIcon } from '@heroicons/react/24/solid';
import { VoteResultsProps } from '../types/app';

const VoteResults: React.FC<VoteResultsProps> = ({ summary }) => {
  if (!summary || summary.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="bg-gray-50 rounded-lg p-6">
          <p className="text-gray-500 text-lg mb-2">No votes yet</p>
          <p className="text-sm text-gray-400">Be the first to vote!</p>
        </div>
      </div>
    );
  }

  const totalVotes = summary.reduce((sum, item) => sum + item.vote_count, 0);

  return (
    <div className="space-y-4">
      {/* Summary Header */}
      <div className="bg-blue-50 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-2">Voting Summary</h3>
        <div className="flex justify-between items-center">
          <p className="text-sm text-blue-700">
            Total votes: <span className="font-medium">{totalVotes}</span>
          </p>
          <p className="text-xs text-blue-600">
            {summary.length} different assessment{summary.length !== 1 ? 's' : ''}
          </p>
        </div>
      </div>

      {/* Results List */}
      <div className="space-y-3">
        {summary.map((item, index) => (
          <ResultCard key={index} item={item} rank={index + 1} />
        ))}
      </div>

      {/* Disclaimer */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
        <p className="text-xs text-yellow-800">
          <strong>Disclaimer:</strong> These results are based on community input and should not replace professional judgment or manufacturer verification. Always consult official documentation for definitive implant identification.
        </p>
      </div>
    </div>
  );
};

interface ResultCardProps {
  item: any; // VoteSummary type
  rank: number;
}

const ResultCard: React.FC<ResultCardProps> = ({ item, rank }) => {
  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1: return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 2: return 'bg-gray-100 text-gray-800 border-gray-200';
      case 3: return 'bg-orange-100 text-orange-800 border-orange-200';
      default: return 'bg-blue-50 text-blue-800 border-blue-200';
    }
  };

  const getProgressBarColor = (rank: number) => {
    switch (rank) {
      case 1: return 'bg-blue-600';
      case 2: return 'bg-blue-500';
      case 3: return 'bg-blue-400';
      default: return 'bg-blue-300';
    }
  };

  return (
    <div className="bg-white border rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1">
          <div className="flex items-center mb-2">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getRankColor(rank)} mr-2`}>
              #{rank}
            </span>
            <h4 className="font-medium text-gray-900">
              {item.brand} {item.model}
            </h4>
          </div>
          <p className="text-sm text-gray-600">
            {item.diameter}mm × {item.length}mm
          </p>
        </div>
        
        <div className="text-right ml-4">
          <div className="text-2xl font-bold text-blue-600">
            {item.percentage}%
          </div>
          <div className="text-xs text-gray-500">
            {item.vote_count} vote{item.vote_count !== 1 ? 's' : ''}
          </div>
        </div>
      </div>
      
      {/* Progress bar */}
      <div className="w-full bg-gray-200 rounded-full h-3 mb-3">
        <div 
          className={`h-3 rounded-full transition-all duration-500 ${getProgressBarColor(rank)}`}
          style={{ width: `${item.percentage}%` }}
        />
      </div>
      
      {/* Confidence indicator */}
      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">
          Avg. Confidence: <span className="font-medium">{item.avg_confidence}/5</span>
        </span>
        <div className="flex items-center">
          {Array.from({ length: 5 }, (_, i) => (
            <StarIcon 
              key={i}
              className={`w-4 h-4 ${
                i < Math.round(item.avg_confidence) 
                  ? 'text-yellow-400' 
                  : 'text-gray-300'
              }`}
            />
          ))}
          <span className="ml-1 text-xs text-gray-500">
            ({item.avg_confidence.toFixed(1)})
          </span>
        </div>
      </div>
    </div>
  );
};

export default VoteResults;
