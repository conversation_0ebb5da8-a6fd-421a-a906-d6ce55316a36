import React, { useState } from 'react';
import { VoteFormProps, VoteFormData, IMPLANT_BRANDS } from '../types/app';
import { ValidationService } from '../utils/validation';
import ErrorDisplay from './common/ErrorDisplay';

const VoteForm: React.FC<VoteFormProps> = ({ onSubmit, isLoading = false }) => {
  const [formData, setFormData] = useState<VoteFormData>({
    brand: '',
    custom_brand: '',
    model: '',
    diameter: '',
    length: '',
    confidence: 5,
    additional_notes: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [validationError, setValidationError] = useState<string>('');

  const validate = (): boolean => {
    const validation = ValidationService.validateVoteSubmission(formData);
    
    if (!validation.valid) {
      setValidationError(validation.errors.join(', '));
      return false;
    }

    setValidationError('');
    return true;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validate()) {
      onSubmit(formData);
    }
  };

  const updateField = (field: keyof VoteFormData, value: string | number) => {
    let processedValue = value;

    // Normalize model text to uppercase
    if (field === 'model' && typeof value === 'string') {
      processedValue = value.toUpperCase();
    }

    setFormData(prev => ({ ...prev, [field]: processedValue }));

    // Clear field-specific error
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Clear validation error
    if (validationError) {
      setValidationError('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {validationError && (
        <ErrorDisplay error={validationError} />
      )}

      {/* Brand */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Implant Brand *
        </label>
        <select
          value={formData.brand}
          onChange={(e) => {
            updateField('brand', e.target.value);
            // Clear custom brand when changing from "Other"
            if (e.target.value !== 'Other') {
              updateField('custom_brand', '');
            }
          }}
          className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
            errors.brand ? 'border-red-300' : 'border-gray-300'
          }`}
          required
        >
          <option value="">Select brand</option>
          {IMPLANT_BRANDS.map(brand => (
            <option key={brand} value={brand}>{brand}</option>
          ))}
        </select>
        {errors.brand && <p className="text-red-500 text-xs mt-1">{errors.brand}</p>}
      </div>

      {/* Custom Brand (shown when "Other" is selected) */}
      {formData.brand === 'Other' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Custom Brand Name *
          </label>
          <input
            type="text"
            value={formData.custom_brand || ''}
            onChange={(e) => updateField('custom_brand', e.target.value.toUpperCase())}
            placeholder="Enter brand name"
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.custom_brand ? 'border-red-300' : 'border-gray-300'
            }`}
            required
          />
          {errors.custom_brand && <p className="text-red-500 text-xs mt-1">{errors.custom_brand}</p>}
        </div>
      )}

      {/* Model */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Model *
        </label>
        <input
          type="text"
          value={formData.model}
          onChange={(e) => updateField('model', e.target.value)}
          placeholder="e.g., Active, Replace Select, TSV"
          className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
            errors.model ? 'border-red-300' : 'border-gray-300'
          }`}
          required
        />
        {errors.model && <p className="text-red-500 text-xs mt-1">{errors.model}</p>}
      </div>

      {/* Diameter and Length */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Diameter (mm) *
          </label>
          <input
            type="number"
            step="0.1"
            min="0"
            value={formData.diameter}
            onChange={(e) => updateField('diameter', e.target.value)}
            placeholder="Enter diameter (e.g., 3.5)"
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.diameter ? 'border-red-300' : 'border-gray-300'
            }`}
            required
          />
          {errors.diameter && <p className="text-red-500 text-xs mt-1">{errors.diameter}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Length (mm) *
          </label>
          <input
            type="number"
            step="0.1"
            min="6"
            max="25"
            value={formData.length}
            onChange={(e) => updateField('length', e.target.value)}
            placeholder="Enter length (e.g., 10)"
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.length ? 'border-red-300' : 'border-gray-300'
            }`}
            required
          />
          {errors.length && <p className="text-red-500 text-xs mt-1">{errors.length}</p>}
        </div>
      </div>

      {/* Confidence Level */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Confidence Level: {formData.confidence}/5
        </label>
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-500">Low</span>
          <input
            type="range"
            min="1"
            max="5"
            value={formData.confidence}
            onChange={(e) => updateField('confidence', parseInt(e.target.value))}
            className="flex-1 mx-3"
          />
          <span className="text-xs text-gray-500">High</span>
        </div>
        <div className="flex justify-between text-xs text-gray-400 mt-1">
          <span>1</span><span>2</span><span>3</span><span>4</span><span>5</span>
        </div>
      </div>

      {/* Additional Notes */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Additional Notes (Optional)
        </label>
        <textarea
          value={formData.additional_notes}
          onChange={(e) => updateField('additional_notes', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          placeholder="Any additional observations or comments..."
          maxLength={500}
        />
        <p className="text-xs text-gray-500 mt-1">
          {formData.additional_notes?.length || 0}/500 characters
        </p>
      </div>

      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium"
      >
        {isLoading ? 'Submitting...' : 'Submit Vote'}
      </button>
    </form>
  );
};

export default VoteForm;
