import React from 'react';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface HeaderProps {
  title: string;
  onBack?: () => void;
  rightElement?: React.ReactNode;
}

const Header: React.FC<HeaderProps> = ({ title, onBack, rightElement }) => {
  return (
    <header className="sticky top-0 bg-white border-b border-gray-200 z-10 safe-area">
      <div className="flex items-center justify-between h-14 px-4">
        <div className="flex items-center flex-1">
          {onBack && (
            <button 
              onClick={onBack} 
              className="mr-3 p-2 -ml-2 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label="Go back"
            >
              <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
            </button>
          )}
          <h1 className="text-lg font-semibold text-gray-900 truncate">
            {title}
          </h1>
        </div>
        
        {rightElement && (
          <div className="flex items-center ml-4">
            {rightElement}
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
