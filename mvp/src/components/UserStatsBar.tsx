import React from 'react';
import { useQuery } from 'react-query';
import { apiService } from '../services/api';
import { DocumentTextIcon, HandThumbUpIcon, TrophyIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from './common/LoadingSpinner';

const UserStatsBar: React.FC = () => {
  const { data: stats, isLoading, error } = useQuery(
    'user-stats',
    () => apiService.getUserStats(),
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
      cacheTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
    }
  );

  // Don't render if there's an error or no data
  if (error || !stats) {
    return null;
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex items-center justify-center">
          <LoadingSpinner size="sm" />
          <span className="ml-2 text-sm text-gray-500">Loading your stats...</span>
        </div>
      </div>
    );
  }

  // Helper function to get rank suffix
  const getRankSuffix = (rank: number): string => {
    if (rank === 0) return '';
    const lastDigit = rank % 10;
    const lastTwoDigits = rank % 100;
    
    if (lastTwoDigits >= 11 && lastTwoDigits <= 13) {
      return 'th';
    }
    
    switch (lastDigit) {
      case 1: return 'st';
      case 2: return 'nd';
      case 3: return 'rd';
      default: return 'th';
    }
  };

  // Helper function to get rank color
  const getRankColor = (rank: number, totalUsers: number): string => {
    if (rank === 0 || totalUsers === 0) return 'text-gray-500';
    
    const percentile = (rank / totalUsers) * 100;
    
    if (percentile <= 10) return 'text-yellow-600'; // Top 10%
    if (percentile <= 25) return 'text-blue-600';   // Top 25%
    if (percentile <= 50) return 'text-green-600';  // Top 50%
    return 'text-gray-600'; // Bottom 50%
  };

  // Helper function to get rank badge
  const getRankBadge = (rank: number, totalUsers: number): string => {
    if (rank === 0 || totalUsers === 0) return '';
    
    const percentile = (rank / totalUsers) * 100;
    
    if (rank === 1) return '👑';
    if (rank === 2) return '🥈';
    if (rank === 3) return '🥉';
    if (percentile <= 10) return '⭐';
    if (percentile <= 25) return '🔥';
    return '';
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900">Your Contribution Stats</h3>
        <div className="text-xs text-gray-500">
          Updated now
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        {/* Posts Created */}
        <div className="text-center">
          <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full mx-auto mb-2">
            <DocumentTextIcon className="w-5 h-5 text-blue-600" />
          </div>
          <div className="text-lg font-semibold text-gray-900">
            {stats.postsCount}
          </div>
          <div className="text-xs text-gray-500">
            Post{stats.postsCount !== 1 ? 's' : ''} Created
          </div>
        </div>

        {/* Votes Contributed */}
        <div className="text-center">
          <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-full mx-auto mb-2">
            <HandThumbUpIcon className="w-5 h-5 text-green-600" />
          </div>
          <div className="text-lg font-semibold text-gray-900">
            {stats.votesCount}
          </div>
          <div className="text-xs text-gray-500">
            Vote{stats.votesCount !== 1 ? 's' : ''} Cast
          </div>
        </div>

        {/* User Rank */}
        <div className="text-center">
          <div className="flex items-center justify-center w-10 h-10 bg-yellow-100 rounded-full mx-auto mb-2">
            <TrophyIcon className="w-5 h-5 text-yellow-600" />
          </div>
          <div className={`text-lg font-semibold ${getRankColor(stats.rank, stats.totalUsers)}`}>
            {stats.rank === 0 ? '-' : (
              <span className="flex items-center justify-center gap-1">
                {getRankBadge(stats.rank, stats.totalUsers)}
                {stats.rank}{getRankSuffix(stats.rank)}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Additional Info */}
      {stats.rank > 0 && stats.totalUsers > 0 && (
        <div className="mt-4 pt-3 border-t border-gray-100">
          <div className="text-center">
            <p className="text-xs text-gray-600">
              {stats.rank === 1 && '🎉 You\'re the top contributor! Amazing work!'}
              {stats.rank === 2 && '🥈 You\'re in 2nd place! Keep it up!'}
              {stats.rank === 3 && '🥉 You\'re in 3rd place! Great job!'}
              {stats.rank > 3 && ((stats.rank / stats.totalUsers) * 100) <= 10 && '⭐ You\'re in the top 10%! Excellent!'}
              {stats.rank > 3 && ((stats.rank / stats.totalUsers) * 100) > 10 && ((stats.rank / stats.totalUsers) * 100) <= 25 && '🔥 You\'re in the top 25%! Well done!'}
              {stats.rank > 3 && ((stats.rank / stats.totalUsers) * 100) > 25 && ((stats.rank / stats.totalUsers) * 100) <= 50 && '👍 You\'re in the top 50%! Good work!'}
              {stats.rank > 3 && ((stats.rank / stats.totalUsers) * 100) > 50 && 'Keep contributing to improve your ranking!'}
            </p>
          </div>
        </div>
      )}

      {/* First-time user encouragement */}
      {stats.postsCount === 0 && stats.votesCount === 0 && (
        <div className="mt-4 pt-3 border-t border-gray-100">
          <div className="text-center">
            <p className="text-xs text-gray-600">
              🚀 Start contributing by creating your first post or voting on existing ones!
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserStatsBar;
