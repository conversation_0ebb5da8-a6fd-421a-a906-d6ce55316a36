import React, { useState } from 'react';
import { 
  ShieldCheckIcon, 
  EyeSlashIcon, 
  ClockIcon,
  InformationCircleIcon 
} from '@heroicons/react/24/outline';
import Modal from './common/Modal';

interface PrivacyNoticeProps {
  type?: 'create' | 'vote' | 'general';
  className?: string;
}

const PrivacyNotice: React.FC<PrivacyNoticeProps> = ({ 
  type = 'general', 
  className = '' 
}) => {
  const [showDetails, setShowDetails] = useState(false);

  const getNoticeContent = () => {
    switch (type) {
      case 'create':
        return {
          title: 'Privacy & Data Handling',
          summary: 'Your post will be visible to verified dental professionals. Images are stored securely and may be used for educational purposes.',
          details: [
            'Images are automatically processed to remove EXIF data',
            'Posts expire after 24 hours and are archived',
            'Only verified dental professionals can vote',
            'No patient identifying information should be included',
            'Data is stored in compliance with HIPAA guidelines',
            'You can request deletion of your posts at any time'
          ]
        };
      case 'vote':
        return {
          title: 'Voting Privacy',
          summary: 'Your votes are anonymous and contribute to community knowledge while protecting your professional identity.',
          details: [
            'Individual votes are anonymous to other users',
            'Only aggregated results are displayed publicly',
            'Your voting history is private to you',
            'Professional credentials are verified but not displayed',
            'Vote data is used for educational research (anonymized)',
            'You can withdraw your vote before results are finalized'
          ]
        };
      default:
        return {
          title: 'Privacy & Security',
          summary: 'We protect your data and maintain professional confidentiality while enabling collaborative learning.',
          details: [
            'End-to-end encryption for sensitive data',
            'Regular security audits and monitoring',
            'GDPR and HIPAA compliant data handling',
            'Professional verification without identity disclosure',
            'Automatic data retention policies',
            'Transparent data usage for educational purposes'
          ]
        };
    }
  };

  const content = getNoticeContent();

  return (
    <>
      <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-start">
          <ShieldCheckIcon className="h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" />
          <div className="flex-1">
            <h4 className="text-sm font-medium text-blue-800 mb-2">
              {content.title}
            </h4>
            <p className="text-xs text-blue-700 mb-3">
              {content.summary}
            </p>
            
            <div className="flex items-center space-x-4 text-xs">
              <div className="flex items-center text-blue-600">
                <EyeSlashIcon className="w-3 h-3 mr-1" />
                <span>Anonymous</span>
              </div>
              <div className="flex items-center text-blue-600">
                <ClockIcon className="w-3 h-3 mr-1" />
                <span>24h Expiry</span>
              </div>
              <button
                onClick={() => setShowDetails(true)}
                className="flex items-center text-blue-600 hover:text-blue-500 underline"
              >
                <InformationCircleIcon className="w-3 h-3 mr-1" />
                <span>Learn More</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Privacy Modal */}
      <Modal
        isOpen={showDetails}
        onClose={() => setShowDetails(false)}
        title={content.title}
        size="lg"
      >
        <div className="space-y-4">
          <p className="text-gray-700">
            {content.summary}
          </p>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Data Protection Measures:</h4>
            <ul className="space-y-2">
              {content.details.map((detail, index) => (
                <li key={index} className="flex items-start text-sm text-gray-600">
                  <ShieldCheckIcon className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>{detail}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <h5 className="text-sm font-medium text-yellow-800 mb-1">
              Important Reminder
            </h5>
            <p className="text-xs text-yellow-700">
              Never include patient names, dates of birth, or other identifying information in images or descriptions. 
              This platform is for educational consultation only and should not replace professional judgment.
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-3">
            <h5 className="text-sm font-medium text-gray-800 mb-2">
              Your Rights
            </h5>
            <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
              <div>• Access your data</div>
              <div>• Request deletion</div>
              <div>• Data portability</div>
              <div>• Withdraw consent</div>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              onClick={() => setShowDetails(false)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm"
            >
              Got it
            </button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default PrivacyNotice;
