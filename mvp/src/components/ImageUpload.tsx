import React from 'react';
import { useDropzone } from 'react-dropzone';
import { PhotoIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { ImageUploadProps } from '../types/app';
import { useImageUpload } from '../hooks/useImageUpload';
import ErrorDisplay from './common/ErrorDisplay';

const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageSelect,
  selectedImage,
  onImageRemove,
}) => {
  const {
    preview,
    validationErrors,
    processingProgress,
    handleFileSelect,
    removeFile,
  } = useImageUpload({
    onSuccess: (imageUrl) => {
      // This will be handled by the parent component
      console.log('Image uploaded:', imageUrl);
    },
  });

  const onDrop = React.useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    await handleFileSelect(file);

    // Call onImageSelect with the file regardless of validation
    // The parent component can decide what to do with validation errors
    onImageSelect(file);
  }, [handleFileSelect, onImageSelect]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { 'image/*': ['.jpeg', '.jpg', '.png'] },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: false,
  });

  const handleRemove = () => {
    removeFile();
    onImageRemove?.();
  };

  if (validationErrors.length > 0) {
    return (
      <div>
        <ErrorDisplay
          error={validationErrors.join(', ')}
          onRetry={() => {/* Errors will clear on next file select */}}
        />
        <div className="mt-4">
          <ImageUploadArea
            getRootProps={getRootProps}
            getInputProps={getInputProps}
            isDragActive={isDragActive}
            validating={processingProgress > 0}
          />
        </div>
      </div>
    );
  }

  if (preview) {
    return (
      <div className="relative">
        <img
          src={preview}
          alt="Preview"
          className="w-full h-64 object-cover rounded-lg border-2 border-gray-300 cursor-pointer"
          onClick={() => window.open(preview, '_blank')}
        />
        <button
          onClick={handleRemove}
          className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          aria-label="Remove image"
        >
          <XMarkIcon className="w-5 h-5" />
        </button>
      </div>
    );
  }

  return (
    <ImageUploadArea
      getRootProps={getRootProps}
      getInputProps={getInputProps}
      isDragActive={isDragActive}
      validating={processingProgress > 0}
      progress={processingProgress}
    />
  );
};

interface ImageUploadAreaProps {
  getRootProps: () => any;
  getInputProps: () => any;
  isDragActive: boolean;
  validating: boolean;
  progress?: number;
}

const ImageUploadArea: React.FC<ImageUploadAreaProps> = ({
  getRootProps,
  getInputProps,
  isDragActive,
  validating,
  progress = 0
}) => {
  return (
    <div
      {...getRootProps()}
      className={`w-full h-64 border-2 border-dashed rounded-lg flex flex-col items-center justify-center cursor-pointer transition-colors ${
        isDragActive 
          ? 'border-blue-400 bg-blue-50' 
          : 'border-gray-300 hover:border-gray-400'
      } ${validating ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      <input {...getInputProps()} disabled={validating} />
      <PhotoIcon className="w-12 h-12 text-gray-400 mb-4" />
      <div className="text-center">
        {validating ? (
          <div>
            <p className="text-gray-600 mb-2">Processing image...</p>
            {progress > 0 && (
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            )}
          </div>
        ) : isDragActive ? (
          <p className="text-blue-600 font-medium">Drop the X-ray image here...</p>
        ) : (
          <>
            <p className="text-gray-600 mb-2">
              <span className="font-medium">Click to upload</span> or drag and drop
            </p>
            <p className="text-sm text-gray-500">
              PNG, JPG up to 10MB
              <br />
              Minimum 100x100 pixels
            </p>
          </>
        )}
      </div>
    </div>
  );
};

export default ImageUpload;
