import React, { useState } from 'react';
import { 
  MagnifyingGlassPlusIcon, 
  MagnifyingGlassMinusIcon,
  ArrowsPointingOutIcon,
  XMarkIcon 
} from '@heroicons/react/24/outline';
import Modal from './common/Modal';

interface ImageViewerProps {
  src: string;
  alt: string;
  className?: string;
  showControls?: boolean;
}

const ImageViewer: React.FC<ImageViewerProps> = ({
  src,
  alt,
  className = '',
  showControls = true,
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [zoom, setZoom] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev * 1.5, 5));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev / 1.5, 0.5));
  };

  const handleReset = () => {
    setZoom(1);
    setPosition({ x: 0, y: 0 });
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (zoom > 1) {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y,
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && zoom > 1) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const openFullscreen = () => {
    setIsFullscreen(true);
    handleReset();
  };

  const closeFullscreen = () => {
    setIsFullscreen(false);
    handleReset();
  };

  const imageStyle = {
    transform: `scale(${zoom}) translate(${position.x / zoom}px, ${position.y / zoom}px)`,
    cursor: zoom > 1 ? (isDragging ? 'grabbing' : 'grab') : 'pointer',
    transition: isDragging ? 'none' : 'transform 0.2s ease-out',
  };

  return (
    <>
      {/* Regular Image */}
      <div className={`relative group ${className}`}>
        <img
          src={src}
          alt={alt}
          className="w-full h-full object-cover rounded-lg cursor-pointer hover:opacity-95 transition-opacity"
          onClick={openFullscreen}
          loading="lazy"
        />
        
        {showControls && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={openFullscreen}
              className="p-2 bg-black bg-opacity-50 text-white rounded-md hover:bg-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black"
              aria-label="View fullscreen"
            >
              <ArrowsPointingOutIcon className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      {/* Fullscreen Modal */}
      <Modal 
        isOpen={isFullscreen} 
        onClose={closeFullscreen}
        size="xl"
        showCloseButton={false}
      >
        <div className="relative bg-black rounded-lg overflow-hidden">
          {/* Controls */}
          <div className="absolute top-4 left-4 z-10 flex space-x-2">
            <button
              onClick={handleZoomIn}
              className="p-2 bg-black bg-opacity-50 text-white rounded-md hover:bg-opacity-70 focus:outline-none focus:ring-2 focus:ring-white"
              aria-label="Zoom in"
            >
              <MagnifyingGlassPlusIcon className="w-5 h-5" />
            </button>
            <button
              onClick={handleZoomOut}
              className="p-2 bg-black bg-opacity-50 text-white rounded-md hover:bg-opacity-70 focus:outline-none focus:ring-2 focus:ring-white"
              aria-label="Zoom out"
            >
              <MagnifyingGlassMinusIcon className="w-5 h-5" />
            </button>
            <button
              onClick={handleReset}
              className="px-3 py-2 bg-black bg-opacity-50 text-white text-sm rounded-md hover:bg-opacity-70 focus:outline-none focus:ring-2 focus:ring-white"
            >
              Reset
            </button>
          </div>

          {/* Close Button */}
          <div className="absolute top-4 right-4 z-10">
            <button
              onClick={closeFullscreen}
              className="p-2 bg-black bg-opacity-50 text-white rounded-md hover:bg-opacity-70 focus:outline-none focus:ring-2 focus:ring-white"
              aria-label="Close fullscreen"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* Zoom Level Indicator */}
          <div className="absolute bottom-4 left-4 z-10">
            <div className="px-3 py-1 bg-black bg-opacity-50 text-white text-sm rounded-md">
              {Math.round(zoom * 100)}%
            </div>
          </div>

          {/* Image Container */}
          <div 
            className="w-full h-96 flex items-center justify-center overflow-hidden select-none"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            <img
              src={src}
              alt={alt}
              className="max-w-full max-h-full object-contain"
              style={imageStyle}
              draggable={false}
            />
          </div>

          {/* Instructions */}
          <div className="absolute bottom-4 right-4 z-10">
            <div className="px-3 py-1 bg-black bg-opacity-50 text-white text-xs rounded-md">
              {zoom > 1 ? 'Drag to pan' : 'Click image to zoom'}
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ImageViewer;
