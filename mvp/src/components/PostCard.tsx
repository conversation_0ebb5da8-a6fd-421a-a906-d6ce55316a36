import React from 'react';
import { ClockIcon, UserIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { PostCardProps } from '../types/app';
import ImageViewer from './ImageViewer';

const PostCard: React.FC<PostCardProps> = ({
  post,
  onVote,
  onViewResults,
  showActions = true,
  onToggleStatus,
  showStatusToggle = false,
}) => {
  const timeAgo = (date: string) => {
    const now = new Date();
    const posted = new Date(date);
    const diffMs = now.getTime() - posted.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    
    if (diffHours < 1) return 'Just now';
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${Math.floor(diffHours / 24)}d ago`;
  };

  const isExpired = () => {
    const now = new Date();
    const expiresAt = new Date(post.expires_at);
    return now > expiresAt;
  };

  return (
    <article 
      className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
      aria-labelledby={`post-title-${post.id}`}
      aria-describedby={`post-description-${post.id}`}
    >
      <div className="relative">
        <ImageViewer
          src={post.image_url}
          alt={`X-ray image from ${timeAgo(post.created_at)}`}
          className="h-48"
          showControls={true}
        />

        {/* Status indicators */}
        <div className="absolute bottom-2 right-2 flex gap-2 items-center">
          {/* Vote count pill badge */}
          <div className={`text-white text-xs px-3 py-1 rounded-full font-medium shadow-sm border-2 border-white/20 ${
            (post.vote_count || 0) === 0
              ? 'bg-gray-500'
              : (post.vote_count || 0) < 3
                ? 'bg-blue-600'
                : 'bg-green-600'
          }`}>
            {(post.vote_count || 0) === 0 ? '0 votes' :
             (post.vote_count || 0) === 1 ? '1 vote' :
             `${post.vote_count || 0} votes`}
          </div>

          {isExpired() && (
            <div className="bg-red-500 text-white text-xs px-2 py-1 rounded">
              Expired
            </div>
          )}

          {showStatusToggle && (
            <div className="flex items-center gap-1">
              <div className={`text-xs px-2 py-1 rounded ${
                post.status === 'active'
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-500 text-white'
              }`}>
                {post.status === 'active' ? 'Active' : 'Hidden'}
              </div>

              <button
                onClick={() => onToggleStatus?.(post.id)}
                className="bg-white bg-opacity-90 hover:bg-opacity-100 p-1 rounded transition-all"
                title={post.status === 'active' ? 'Hide from board' : 'Show on board'}
              >
                {post.status === 'active' ? (
                  <EyeSlashIcon className="h-4 w-4 text-gray-600" />
                ) : (
                  <EyeIcon className="h-4 w-4 text-gray-600" />
                )}
              </button>
            </div>
          )}
        </div>
      </div>
      
      <div className="p-4">
        <h3 id={`post-title-${post.id}`} className="sr-only">
          Dental implant identification request
        </h3>
        
        {post.description && (
          <p 
            id={`post-description-${post.id}`}
            className="text-gray-700 mb-3 text-sm leading-relaxed"
          >
            {post.description}
          </p>
        )}
        
        <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
          <div className="flex items-center">
            <UserIcon className="w-4 h-4 mr-1" />
            <span>Anonymous</span>
          </div>
          <div className="flex items-center">
            <ClockIcon className="w-4 h-4 mr-1" />
            <span>{timeAgo(post.created_at)}</span>
          </div>
        </div>

        {showActions && !isExpired() && (
          <div className="flex space-x-2">
            <button
              onClick={onVote}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors text-sm font-medium"
              aria-label={`Vote on implant identification for case ${post.id}`}
            >
              Vote
            </button>
            <button
              onClick={onViewResults}
              className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors text-sm font-medium"
              aria-label={`View voting results for case ${post.id}`}
            >
              Results
            </button>
          </div>
        )}

        {isExpired() && (
          <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
            <p className="text-sm text-gray-600 text-center">
              This post has expired and is no longer accepting votes
            </p>
            {showActions && (
              <button
                onClick={onViewResults}
                className="w-full mt-2 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors text-sm font-medium"
              >
                View Final Results
              </button>
            )}
          </div>
        )}
      </div>
    </article>
  );
};

export default PostCard;
