import React from 'react';
import { 
  ChartBarIcon, 
  UserGroupIcon, 
  CheckCircleIcon,
  ClockIcon 
} from '@heroicons/react/24/outline';
import Badge from './common/Badge';

interface VotingStatsProps {
  totalVotes: number;
  uniqueAssessments: number;
  consensusLevel: number;
  hasStrongConsensus: boolean;
  confidenceDistribution: Record<number, number>;
  className?: string;
}

const VotingStats: React.FC<VotingStatsProps> = ({
  totalVotes,
  uniqueAssessments,
  consensusLevel,
  hasStrongConsensus,
  confidenceDistribution,
  className = '',
}) => {
  const getConsensusColor = () => {
    if (consensusLevel >= 0.8) return 'text-green-600';
    if (consensusLevel >= 0.6) return 'text-yellow-600';
    return 'text-gray-600';
  };

  const getConsensusLabel = () => {
    if (consensusLevel >= 0.8) return 'Strong Consensus';
    if (consensusLevel >= 0.6) return 'Moderate Consensus';
    if (consensusLevel >= 0.4) return 'Weak Consensus';
    return 'No Consensus';
  };

  const avgConfidence = Object.entries(confidenceDistribution).reduce(
    (acc, [confidence, count]) => acc + (parseInt(confidence) * count),
    0
  ) / Math.max(totalVotes, 1);

  return (
    <div className={`bg-white rounded-lg p-4 border ${className}`}>
      <h3 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
        <ChartBarIcon className="w-4 h-4 mr-2" />
        Voting Statistics
      </h3>

      <div className="grid grid-cols-2 gap-4 mb-4">
        {/* Total Votes */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <UserGroupIcon className="w-4 h-4 text-blue-500 mr-1" />
            <span className="text-2xl font-bold text-blue-600">{totalVotes}</span>
          </div>
          <p className="text-xs text-gray-500">Total Votes</p>
        </div>

        {/* Unique Assessments */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <ClockIcon className="w-4 h-4 text-purple-500 mr-1" />
            <span className="text-2xl font-bold text-purple-600">{uniqueAssessments}</span>
          </div>
          <p className="text-xs text-gray-500">Different Views</p>
        </div>
      </div>

      {/* Consensus Level */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Consensus Level</span>
          <Badge 
            variant={hasStrongConsensus ? 'success' : consensusLevel >= 0.4 ? 'warning' : 'default'}
            size="sm"
          >
            {getConsensusLabel()}
          </Badge>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-500 ${
              consensusLevel >= 0.8 ? 'bg-green-500' :
              consensusLevel >= 0.6 ? 'bg-yellow-500' :
              consensusLevel >= 0.4 ? 'bg-orange-500' : 'bg-gray-400'
            }`}
            style={{ width: `${consensusLevel * 100}%` }}
          />
        </div>
        
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>0%</span>
          <span className={getConsensusColor()}>
            {Math.round(consensusLevel * 100)}%
          </span>
          <span>100%</span>
        </div>
      </div>

      {/* Average Confidence */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Average Confidence</span>
          <div className="flex items-center">
            {Array.from({ length: 5 }, (_, i) => (
              <div
                key={i}
                className={`w-2 h-2 rounded-full mr-1 ${
                  i < Math.round(avgConfidence) ? 'bg-yellow-400' : 'bg-gray-300'
                }`}
              />
            ))}
            <span className="text-sm font-medium text-gray-700 ml-1">
              {avgConfidence.toFixed(1)}/5
            </span>
          </div>
        </div>
      </div>

      {/* Confidence Distribution */}
      {Object.keys(confidenceDistribution).length > 0 && (
        <div>
          <h4 className="text-xs font-medium text-gray-700 mb-2">Confidence Distribution</h4>
          <div className="space-y-1">
            {[5, 4, 3, 2, 1].map(level => {
              const count = confidenceDistribution[level] || 0;
              const percentage = totalVotes > 0 ? (count / totalVotes) * 100 : 0;
              
              return (
                <div key={level} className="flex items-center text-xs">
                  <span className="w-8 text-gray-600">{level}★</span>
                  <div className="flex-1 bg-gray-200 rounded-full h-1.5 mx-2">
                    <div 
                      className="bg-yellow-400 h-1.5 rounded-full transition-all duration-300"
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                  <span className="w-8 text-right text-gray-500">{count}</span>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Quality Indicators */}
      {totalVotes >= 3 && (
        <div className="mt-4 pt-3 border-t border-gray-200">
          <div className="flex items-center justify-center space-x-4 text-xs">
            {hasStrongConsensus && (
              <div className="flex items-center text-green-600">
                <CheckCircleIcon className="w-3 h-3 mr-1" />
                <span>High Confidence</span>
              </div>
            )}
            {totalVotes >= 5 && (
              <div className="flex items-center text-blue-600">
                <UserGroupIcon className="w-3 h-3 mr-1" />
                <span>Well Reviewed</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default VotingStats;
