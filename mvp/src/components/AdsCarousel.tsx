import React, { useState, useEffect, useCallback } from 'react';
import { useQuery } from 'react-query';
import { apiService } from '../services/api';
import { AdData } from '../types/database';
import LoadingSpinner from './common/LoadingSpinner';

const AdsCarousel: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Fetch ads data
  const { data: ads, isLoading, error } = useQuery(
    'ads-data',
    () => apiService.getAdsData(),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
    }
  );

  // Auto-advance slides every 5 seconds
  useEffect(() => {
    if (!ads || ads.length <= 1 || !isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => {
        const nextSlide = (prev + 1) % ads.length;
        return nextSlide;
      });
    }, 5000);

    return () => clearInterval(interval);
  }, [ads, isAutoPlaying]);

  // Safety check to ensure currentSlide is within bounds
  useEffect(() => {
    if (ads && currentSlide >= ads.length) {
      setCurrentSlide(0);
    }
  }, [ads, currentSlide]);

  // Handle manual navigation
  const goToSlide = useCallback((index: number) => {
    if (!ads || index < 0 || index >= ads.length) return;
    setCurrentSlide(index);
    setIsAutoPlaying(false);
    // Resume auto-play after 10 seconds of inactivity
    setTimeout(() => setIsAutoPlaying(true), 10000);
  }, [ads]);

  const nextSlide = useCallback(() => {
    if (!ads || ads.length === 0) return;
    const nextIndex = (currentSlide + 1) % ads.length;
    goToSlide(nextIndex);
  }, [ads, currentSlide, goToSlide]);

  const prevSlide = useCallback(() => {
    if (!ads || ads.length === 0) return;
    const prevIndex = currentSlide === 0 ? ads.length - 1 : currentSlide - 1;
    goToSlide(prevIndex);
  }, [ads, currentSlide, goToSlide]);

  // Handle ad click
  const handleAdClick = useCallback((ad: AdData) => {
    try {
      // Open in new tab/window
      window.open(ad.redirect_url, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('Error opening ad URL:', error);
    }
  }, []);

  // Touch/swipe handling for mobile
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      nextSlide();
    } else if (isRightSwipe) {
      prevSlide();
    }
  };

  // Don't render if no ads or error
  if (error || !ads || ads.length === 0) {
    return null;
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="w-full h-32 bg-gray-100 rounded-lg flex items-center justify-center">
        <LoadingSpinner size="sm" />
      </div>
    );
  }

  return (
    <div className="relative w-full bg-white rounded-lg shadow-sm overflow-hidden">
      {/* Carousel Container */}
      <div
        className="relative h-32 sm:h-40 overflow-hidden"
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >
        {/* Slides */}
        <div
          className="flex transition-transform duration-500 ease-in-out h-full"
          style={{
            transform: `translateX(-${currentSlide * 100}%)`,
          }}
        >
          {ads.map((ad, index) => (
            <div
              key={`${ad.customer_id}-${index}`}
              className="w-full h-full flex-shrink-0 cursor-pointer relative group"
              onClick={() => handleAdClick(ad)}
            >
              {/* Ad Image */}
              <img
                src={ad.image_url}
                alt={`Advertisement by ${ad.customer_name}`}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                loading="lazy"
                onError={(e) => {
                  // Fallback for broken images
                  const target = e.target as HTMLImageElement;
                  target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjE2MCIgdmlld0JveD0iMCAwIDQwMCAxNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMTYwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iODAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlDQTNBRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSI+QWQgSW1hZ2UgTm90IEF2YWlsYWJsZTwvdGV4dD4KPC9zdmc+';
                }}
              />
              
              {/* Overlay with customer name */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3">
                <p className="text-white text-xs font-medium truncate">
                  {ad.customer_name}
                </p>
              </div>

              {/* Hover overlay */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Arrows - Only show if more than 1 ad */}
      {ads.length > 1 && (
        <>
          <button
            onClick={prevSlide}
            className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-700 rounded-full p-1.5 shadow-md transition-all duration-200 hover:scale-110"
            aria-label="Previous advertisement"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          
          <button
            onClick={nextSlide}
            className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-700 rounded-full p-1.5 shadow-md transition-all duration-200 hover:scale-110"
            aria-label="Next advertisement"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      )}

      {/* Dots Indicator - Only show if more than 1 ad */}
      {ads.length > 1 && (
        <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex space-x-1">
          {ads.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-2 h-2 rounded-full transition-all duration-200 ${
                index === currentSlide
                  ? 'bg-white scale-125'
                  : 'bg-white/60 hover:bg-white/80'
              }`}
              aria-label={`Go to advertisement ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default AdsCarousel;
