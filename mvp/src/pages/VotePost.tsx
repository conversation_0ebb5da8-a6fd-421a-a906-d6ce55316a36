import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useQuery } from 'react-query';
import { apiService } from '../services/api';
import { useVoting } from '../hooks/useVoting';
import VoteForm from '../components/VoteForm';
import VoteResults from '../components/VoteResults';
import VotingStats from '../components/VotingStats';
import ImageViewer from '../components/ImageViewer';
import Header from '../components/common/Header';
import LoadingSpinner from '../components/common/LoadingSpinner';
import ErrorDisplay from '../components/common/ErrorDisplay';

const VotePost: React.FC = () => {
  const { postId } = useParams<{ postId: string }>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState<'vote' | 'results'>('vote');

  useEffect(() => {
    if (searchParams.get('view') === 'results') {
      setActiveTab('results');
    }
  }, [searchParams]);

  const { data: post, isLoading: postLoading, error: postError } = useQuery(
    ['post', postId],
    () => apiService.getPost(postId!),
    { enabled: !!postId }
  );

  const voting = useVoting({
    postId: postId!,
    onVoteSuccess: () => setActiveTab('results'),
  });

  if (postLoading || voting.checkingVoteStatus) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header title="Loading..." onBack={() => navigate('/board')} />
        <div className="flex items-center justify-center py-20">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (postError || !post) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header title="Post Not Found" onBack={() => navigate('/board')} />
        <ErrorDisplay 
          error="Post not found or has been removed." 
          onRetry={() => navigate('/board')}
        />
      </div>
    );
  }

  const isExpired = new Date() > new Date(post.expires_at);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        title={activeTab === 'vote' ? 'Submit Vote' : 'Vote Results'} 
        onBack={() => navigate('/board')} 
      />

      <div className="max-w-md mx-auto px-4 py-6">
        {/* Post Image */}
        <div className="mb-6">
          <div className="relative">
            <ImageViewer
              src={post.image_url}
              alt="X-ray for implant identification"
              className="h-64"
              showControls={true}
            />
            {isExpired && (
              <div className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
                Expired
              </div>
            )}
          </div>
          
          {post.description && (
            <div className="mt-3 bg-white rounded-lg p-3 border">
              <h4 className="text-sm font-medium text-gray-900 mb-1">Case Description</h4>
              <p className="text-sm text-gray-700">{post.description}</p>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className="flex mb-6 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('vote')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'vote'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
            disabled={isExpired}
          >
            {voting.hasVoted ? 'Your Vote' : 'Vote'}
          </button>
          <button
            onClick={() => setActiveTab('results')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'results'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Results {voting.votingStats.totalVotes > 0 && `(${voting.votingStats.totalVotes})`}
          </button>
        </div>

        {/* Content */}
        {activeTab === 'vote' ? (
          <VoteContent
            hasVoted={voting.hasVoted}
            isExpired={isExpired}
            onSubmit={voting.submitVote}
            isLoading={voting.isSubmitting}
            onSwitchToResults={() => setActiveTab('results')}
          />
        ) : (
          <div className="space-y-6">
            {/* Voting Statistics */}
            <VotingStats
              totalVotes={voting.votingStats.totalVotes}
              uniqueAssessments={voting.votingStats.uniqueAssessments}
              consensusLevel={voting.votingStats.consensusLevel}
              hasStrongConsensus={voting.hasStrongConsensus}
              confidenceDistribution={voting.confidenceDistribution}
            />

            {/* Vote Results */}
            {voting.loadingSummary ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : (
              <VoteResults summary={voting.voteSummary || []} />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

interface VoteContentProps {
  hasVoted?: boolean;
  isExpired: boolean;
  onSubmit: (data: any) => void;
  isLoading: boolean;
  onSwitchToResults: () => void;
}

const VoteContent: React.FC<VoteContentProps> = ({
  hasVoted,
  isExpired,
  onSubmit,
  isLoading,
  onSwitchToResults
}) => {
  if (isExpired) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h3 className="text-red-800 font-medium mb-2">Post Expired</h3>
        <p className="text-red-700 text-sm mb-3">
          This post has expired and is no longer accepting votes.
        </p>
        <button
          onClick={onSwitchToResults}
          className="text-red-600 hover:text-red-500 underline text-sm"
        >
          View final results
        </button>
      </div>
    );
  }

  if (hasVoted) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h3 className="text-green-800 font-medium mb-2">✓ Vote Submitted</h3>
        <p className="text-green-700 text-sm mb-3">
          You have already voted on this post. Thank you for your contribution!
        </p>
        <button
          onClick={onSwitchToResults}
          className="text-green-600 hover:text-green-500 underline text-sm"
        >
          View current results
        </button>
      </div>
    );
  }

  return <VoteForm onSubmit={onSubmit} isLoading={isLoading} />;
};

export default VotePost;
