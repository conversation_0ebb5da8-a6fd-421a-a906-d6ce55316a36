import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation } from 'react-query';
import toast from 'react-hot-toast';
import { apiService } from '../services/api';
import { ValidationService } from '../utils/validation';
import ImageUpload from '../components/ImageUpload';
import PrivacyNotice from '../components/PrivacyNotice';
import Header from '../components/common/Header';
import ErrorDisplay from '../components/common/ErrorDisplay';

const CreatePost: React.FC = () => {
  const navigate = useNavigate();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [description, setDescription] = useState('');
  const [validationError, setValidationError] = useState('');

  const createPostMutation = useMutation(
    ({ file, desc }: { file: File; desc: string }) =>
      apiService.createPost(file, desc),
    {
      onSuccess: () => {
        toast.success('Post created successfully!');
        navigate('/board');
      },
      onError: (error: any) => {
        toast.error(error.message || 'Failed to create post');
      },
    }
  );

  const validateForm = (): boolean => {
    // Check rate limiting
    // if (!ValidationService.validateRateLimit('create_post', 'current_user')) {
    //   setValidationError('You have reached the posting limit. Please try again later.');
    //   return false;
    // }

    // Validate image
    if (!selectedFile) {
      setValidationError('Please select an image');
      return false;
    }

    // Validate description
    const descValidation = ValidationService.validatePostCreation(description);
    if (!descValidation.valid) {
      setValidationError(descValidation.errors.join(', '));
      return false;
    }

    setValidationError('');
    return true;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const sanitizedDescription = ValidationService.sanitizeText(description);
    createPostMutation.mutate({ 
      file: selectedFile!, 
      desc: sanitizedDescription 
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        title="Create Post" 
        onBack={() => navigate('/')} 
      />

      <div className="max-w-md mx-auto px-4 py-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {validationError && (
            <ErrorDisplay 
              error={validationError} 
              onRetry={() => setValidationError('')} 
            />
          )}

          {/* Medical Disclaimer */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">
              Important Guidelines
            </h3>
            <ul className="text-xs text-yellow-700 space-y-1">
              <li>• Remove all patient identifying information from images</li>
              <li>• Ensure image quality is sufficient for assessment</li>
              <li>• Provide relevant clinical context if helpful</li>
              <li>• This is for educational consultation only</li>
            </ul>
          </div>

          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              X-ray Image *
            </label>
            <ImageUpload
              onImageSelect={(file) => {
                console.log('File selected:', file?.name, file?.size);
                setSelectedFile(file);
                // Clear validation error when file is selected
                if (validationError === 'Please select an image') {
                  setValidationError('');
                }
              }}
              selectedImage={selectedFile || undefined}
              onImageRemove={() => {
                setSelectedFile(null);
                setValidationError('');
              }}
            />
            <p className="text-xs text-gray-500 mt-2">
              Accepted formats: JPEG, PNG. Maximum size: 10MB.
            </p>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Case Description (Optional)
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 resize-none"
              placeholder="Provide any relevant clinical information that might help with identification (e.g., patient age, implant placement date, clinical observations)..."
              maxLength={1000}
            />
            <div className="flex justify-between items-center mt-1">
              <p className="text-xs text-gray-500">
                {description.length}/1000 characters
              </p>
              <p className="text-xs text-gray-400">
                No patient identifiers
              </p>
            </div>
          </div>

          {/* Privacy Notice */}
          <PrivacyNotice type="create" />

          {/* Submit Button */}
          <button
            type="submit"
            disabled={!selectedFile || createPostMutation.isLoading}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium"
          >
            {createPostMutation.isLoading ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating Post...
              </span>
            ) : (
              'Create Post'
            )}
          </button>

          {/* Help Text */}
          <div className="text-center">
            <p className="text-xs text-gray-500">
              Need help? Check our{' '}
              <button 
                type="button"
                className="text-blue-600 hover:text-blue-500 underline"
                onClick={() => {/* Open help modal */}}
              >
                posting guidelines
              </button>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreatePost;
