import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import { apiService } from '../services/api';
import PostCard from '../components/PostCard';
import Header from '../components/common/Header';
import LoadingSpinner from '../components/common/LoadingSpinner';
import ErrorDisplay from '../components/common/ErrorDisplay';

type TabType = 'active' | 'expired' | 'search';

interface SearchFilters {
  brand: string;
  customBrand: string;
  model: string;
}

const VotingBoard: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<TabType>('active');
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    brand: '',
    customBrand: '',
    model: ''
  });

  // Fetch all posts (both active and expired)
  const { data: allPosts, isLoading, error, refetch } = useQuery(
    'all-posts',
    () => apiService.getAllPosts(),
    {
      refetchInterval: 30000, // Refresh every 30 seconds
      staleTime: 10000, // Consider data stale after 10 seconds
    }
  );

  // Fetch search results when in search tab
  const { data: searchResults, isLoading: isSearchLoading } = useQuery(
    ['search-posts', searchFilters],
    () => apiService.searchPosts(searchFilters),
    {
      enabled: activeTab === 'search' && Boolean(searchFilters.brand || searchFilters.model),
      staleTime: 30000,
    }
  );

  // Compute active and expired posts
  const { activePosts, expiredPosts } = useMemo(() => {
    if (!allPosts) return { activePosts: [], expiredPosts: [] };

    const now = new Date();
    const active = allPosts.filter(post => {
      const expiresAt = new Date(post.expires_at);
      return post.status === 'active' && now <= expiresAt;
    });

    const expired = allPosts.filter(post => {
      const expiresAt = new Date(post.expires_at);
      return post.status === 'active' && now > expiresAt;
    }).slice(0, 50); // Top 50 most recent expired posts

    return { activePosts: active, expiredPosts: expired };
  }, [allPosts]);

  // Get current posts based on active tab
  const currentPosts = useMemo(() => {
    switch (activeTab) {
      case 'active':
        return activePosts;
      case 'expired':
        return expiredPosts;
      case 'search':
        return searchResults || [];
      default:
        return activePosts;
    }
  }, [activeTab, activePosts, expiredPosts, searchResults]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header title="Voting Board" onBack={() => navigate('/')} />
        <div className="flex items-center justify-center py-20">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header title="Voting Board" onBack={() => navigate('/')} />
        <ErrorDisplay 
          error="Failed to load posts. Please check your connection and try again." 
          onRetry={() => refetch()}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header
        title="Voting Board"
        onBack={() => navigate('/')}
        rightElement={
          <button
            onClick={() => refetch()}
            className="p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md"
            aria-label="Refresh posts"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        }
      />

      {/* Sticky Tab Navigation */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-md mx-auto px-4">
          <div className="flex space-x-0">
            <button
              onClick={() => setActiveTab('active')}
              className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'active'
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Active ({activePosts.length})
            </button>
            <button
              onClick={() => setActiveTab('expired')}
              className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'expired'
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Expired ({expiredPosts.length})
            </button>
            <button
              onClick={() => setActiveTab('search')}
              className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'search'
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Search
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-md mx-auto px-4 py-6">
        {/* Search Form - Only show when search tab is active */}
        {activeTab === 'search' && (
          <SearchForm
            filters={searchFilters}
            onFiltersChange={setSearchFilters}
            isLoading={isSearchLoading}
          />
        )}

        {/* Info Banner - Only show for active tab */}
        {activeTab === 'active' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="text-sm font-medium text-blue-800 mb-1">
              Help Identify Dental Implants
            </h3>
            <p className="text-xs text-blue-700">
              Share your expertise to help colleagues identify implant brands, models, and specifications. Your votes contribute to community knowledge.
            </p>
          </div>
        )}

        {/* Expired Posts Info Banner */}
        {activeTab === 'expired' && (
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
            <h3 className="text-sm font-medium text-amber-800 mb-1">
              Expired Posts
            </h3>
            <p className="text-xs text-amber-700">
              These posts are no longer accepting votes but you can still view the final results.
            </p>
          </div>
        )}

        {/* Posts List */}
        {currentPosts.length === 0 ? (
          <EmptyState
            activeTab={activeTab}
            onCreatePost={() => navigate('/create')}
          />
        ) : (
          <div className="space-y-4">
            {currentPosts.map((post) => (
              <PostCard
                key={post.id}
                post={post}
                onVote={() => navigate(`/vote/${post.id}`)}
                onViewResults={() => navigate(`/vote/${post.id}?view=results`)}
                showActions={true}
              />
            ))}
          </div>
        )}

        {/* Results Summary */}
        {currentPosts.length > 0 && (
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500">
              Showing {currentPosts.length} {activeTab} post{currentPosts.length !== 1 ? 's' : ''}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

const EmptyState: React.FC<{
  activeTab: TabType;
  onCreatePost: () => void
}> = ({ activeTab, onCreatePost }) => {
  const getEmptyStateContent = () => {
    switch (activeTab) {
      case 'active':
        return {
          title: 'No Active Posts',
          description: 'There are currently no active posts available for voting. Be the first to share a case!',
          showCreateButton: true
        };
      case 'expired':
        return {
          title: 'No Expired Posts',
          description: 'There are no expired posts to display.',
          showCreateButton: false
        };
      case 'search':
        return {
          title: 'No Search Results',
          description: 'No posts found matching your search criteria. Try adjusting your filters.',
          showCreateButton: false
        };
      default:
        return {
          title: 'No Posts Available',
          description: 'There are currently no posts available.',
          showCreateButton: true
        };
    }
  };

  const content = getEmptyStateContent();

  return (
    <div className="text-center py-12">
      <div className="bg-white rounded-lg p-8 shadow-sm">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>

        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {content.title}
        </h3>
        <p className="text-gray-600 mb-6 text-sm">
          {content.description}
        </p>

        {content.showCreateButton && (
          <button
            onClick={onCreatePost}
            className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium"
          >
            Create the First Post
          </button>
        )}
      </div>
    </div>
  );
};

const SearchForm: React.FC<{
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  isLoading: boolean;
}> = ({ filters, onFiltersChange, isLoading }) => {
  const brandOptions = [
    'STRAUMANN', 'NOBEL BIOCARE', 'ZIMMER BIOMET', 'DENTSPLY SIRONA',
    'OSSTEM', 'MEGAGEN', 'HIOSSEN', 'DENTIUM', 'NEOBIOTECH', 'OTHER'
  ];

  const handleSearch = () => {
    // Search is triggered automatically by the useQuery dependency
  };

  return (
    <div className="bg-white rounded-lg p-4 mb-6 shadow-sm border border-gray-200">
      <h3 className="text-sm font-medium text-gray-900 mb-4">Search Posts</h3>

      <div className="space-y-4">
        {/* Brand Dropdown */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Brand
          </label>
          <select
            value={filters.brand}
            onChange={(e) => onFiltersChange({ ...filters, brand: e.target.value, customBrand: '' })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select brand...</option>
            {brandOptions.map(brand => (
              <option key={brand} value={brand}>{brand}</option>
            ))}
          </select>
        </div>

        {/* Custom Brand Input */}
        {filters.brand === 'OTHER' && (
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Custom Brand
            </label>
            <input
              type="text"
              value={filters.customBrand}
              onChange={(e) => onFiltersChange({ ...filters, customBrand: e.target.value })}
              placeholder="Enter brand name..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        )}

        {/* Model Input */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Model
          </label>
          <input
            type="text"
            value={filters.model}
            onChange={(e) => onFiltersChange({ ...filters, model: e.target.value })}
            placeholder="Enter model name..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Search Button */}
        <button
          onClick={handleSearch}
          disabled={isLoading || (!filters.brand && !filters.model)}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors text-sm font-medium disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Searching...' : 'Search'}
        </button>
      </div>
    </div>
  );
};

export default VotingBoard;
