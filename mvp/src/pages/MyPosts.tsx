import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';
import { apiService } from '../services/api';
import PostCard from '../components/PostCard';
import Header from '../components/common/Header';
import LoadingSpinner from '../components/common/LoadingSpinner';
import ErrorDisplay from '../components/common/ErrorDisplay';

const MyPosts: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const { data: posts, isLoading, error, refetch } = useQuery(
    'myPosts',
    () => apiService.getMyPosts(),
    {
      staleTime: 30000, // Consider data stale after 30 seconds
    }
  );

  // Toggle post status mutation
  const toggleStatusMutation = useMutation(
    (postId: string) => apiService.togglePostStatus(postId),
    {
      onSuccess: () => {
        toast.success('Post status updated successfully!');
        queryClient.invalidateQueries('myPosts');
      },
      onError: (error: any) => {
        toast.error(error.message || 'Failed to update post status');
      },
    }
  );

  const handleToggleStatus = (postId: string) => {
    toggleStatusMutation.mutate(postId);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header title="My Posts" onBack={() => navigate('/')} />
        <div className="flex items-center justify-center py-20">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header title="My Posts" onBack={() => navigate('/')} />
        <ErrorDisplay 
          error="Failed to load your posts. Please try again." 
          onRetry={() => refetch()}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        title="My Posts" 
        onBack={() => navigate('/')}
        rightElement={
          <button
            onClick={() => refetch()}
            className="p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md"
            aria-label="Refresh posts"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        }
      />

      <div className="max-w-md mx-auto px-4 py-6">
        {/* Summary Stats */}
        {posts && posts.length > 0 && (
          <div className="bg-white rounded-lg p-4 mb-6 shadow-sm">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Your Activity</h3>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">{posts.length}</div>
                <div className="text-xs text-gray-500">Total Posts</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {posts.filter(p => p.status === 'active').length}
                </div>
                <div className="text-xs text-gray-500">Active</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-600">
                  {posts.filter(p => new Date() > new Date(p.expires_at)).length}
                </div>
                <div className="text-xs text-gray-500">Expired</div>
              </div>
            </div>
          </div>
        )}

        {/* Posts List */}
        {posts?.length === 0 ? (
          <EmptyState onCreatePost={() => navigate('/create')} />
        ) : (
          <div className="space-y-4">
            {posts?.map((post) => (
              <div key={post.id} className="relative">
                <PostCard
                  post={post}
                  onViewResults={() => navigate(`/vote/${post.id}?view=results`)}
                  showActions={true}
                  onToggleStatus={handleToggleStatus}
                  showStatusToggle={true}
                />
                
                {/* Post Status Indicator */}
                <div className="absolute top-2 left-2">
                  <PostStatusBadge post={post} />
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Help Text */}
        {posts && posts.length > 0 && (
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-800 mb-2">
              Post Management Tips
            </h4>
            <ul className="text-xs text-blue-700 space-y-1">
              <li>• Posts automatically expire after 24 hours</li>
              <li>• You can view results even after expiration</li>
              <li>• Active posts continue to receive votes</li>
              <li>• All images are stored securely and anonymously</li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

const EmptyState: React.FC<{ onCreatePost: () => void }> = ({ onCreatePost }) => {
  return (
    <div className="text-center py-12">
      <div className="bg-white rounded-lg p-8 shadow-sm">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </div>
        
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No Posts Yet
        </h3>
        <p className="text-gray-600 mb-6 text-sm">
          You haven't created any posts yet. Share your first case to get expert opinions from the community.
        </p>
        
        <button
          onClick={onCreatePost}
          className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium"
        >
          Create Your First Post
        </button>
      </div>
    </div>
  );
};

interface PostStatusBadgeProps {
  post: any; // Post type
}

const PostStatusBadge: React.FC<PostStatusBadgeProps> = ({ post }) => {
  const isExpired = new Date() > new Date(post.expires_at);
  const isActive = post.status === 'active' && !isExpired;

  if (isActive) {
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
        Active
      </span>
    );
  }

  if (isExpired) {
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
        Expired
      </span>
    );
  }

  return (
    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
      {post.status}
    </span>
  );
};

export default MyPosts;
