import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { PlusIcon, ViewfinderCircleIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/common/LoadingSpinner';
import AdsCarousel from '../components/AdsCarousel';
import UserStatsBar from '../components/UserStatsBar';

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { user, loading, login } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4">
        <div className="text-center max-w-md">
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Implant Identification
            </h1>
            <p className="text-xl text-gray-600 mb-2">
              Get expert opinions on dental implant identification
            </p>
            <p className="text-sm text-gray-500">
              Connect with dental professionals worldwide
            </p>
          </div>
          
          <div className="bg-white rounded-lg p-6 shadow-lg mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">
              How it works
            </h2>
            <div className="space-y-3 text-sm text-gray-600">
              <div className="flex items-start">
                <span className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</span>
                <span>Upload X-ray images of dental implants</span>
              </div>
              <div className="flex items-start">
                <span className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</span>
                <span>Get expert assessments from verified professionals</span>
              </div>
              <div className="flex items-start">
                <span className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</span>
                <span>View consensus results with confidence levels</span>
              </div>
            </div>
          </div>

          <button
            onClick={login}
            className="w-full bg-line-green text-white px-8 py-4 rounded-lg text-lg font-medium hover:bg-line-darkgreen transition-colors shadow-lg"
          >
            Login with LINE
          </button>
          
          <p className="text-xs text-gray-500 mt-4">
            For dental professionals only. By logging in, you agree to our terms of service.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-md mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold text-gray-900">Implant Voting</h1>
              <p className="text-sm text-gray-600">Welcome back, {user.displayName}</p>
            </div>
            {user.pictureUrl && (
              <img
                src={user.pictureUrl}
                alt={user.displayName}
                className="w-10 h-10 rounded-full border-2 border-gray-200"
              />
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-md mx-auto px-4 py-6">
        {/* User Stats Bar */}
        <UserStatsBar />

        <div className="grid grid-cols-1 gap-4">
          <ActionCard
            icon={<PlusIcon className="w-6 h-6 text-blue-600" />}
            title="Create Post"
            description="Upload X-ray for identification"
            onClick={() => navigate('/create')}
            bgColor="bg-blue-100"
          />

          <ActionCard
            icon={<ViewfinderCircleIcon className="w-6 h-6 text-green-600" />}
            title="Voting Board"
            description="Help identify implants"
            onClick={() => navigate('/board')}
            bgColor="bg-green-100"
          />

          <ActionCard
            icon={<DocumentTextIcon className="w-6 h-6 text-purple-600" />}
            title="My Posts"
            description="View your submissions"
            onClick={() => navigate('/my-posts')}
            bgColor="bg-purple-100"
          />
        </div>

        {/* Medical Disclaimer */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-yellow-800 mb-2">
            Medical Professional Use Only
          </h3>
          <p className="text-xs text-yellow-700">
            This tool is for educational and consultation purposes. Always verify implant identification through multiple sources and manufacturer documentation.
          </p>
        </div>

        {/* Advertisement Carousel */}
        {/* <div className="mt-6">
          <AdsCarousel />
        </div> */}
      </div>
    </div>
  );
};

interface ActionCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  onClick: () => void;
  bgColor: string;
}

const ActionCard: React.FC<ActionCardProps> = ({ icon, title, description, onClick, bgColor }) => {
  return (
    <button
      onClick={onClick}
      className="bg-white p-6 rounded-lg shadow-md flex items-center space-x-4 hover:shadow-lg transition-shadow text-left w-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
    >
      <div className={`${bgColor} p-3 rounded-full flex-shrink-0`}>
        {icon}
      </div>
      <div className="flex-1 min-w-0">
        <h2 className="font-semibold text-gray-900 truncate">{title}</h2>
        <p className="text-gray-600 text-sm">{description}</p>
      </div>
    </button>
  );
};

export default Home;
