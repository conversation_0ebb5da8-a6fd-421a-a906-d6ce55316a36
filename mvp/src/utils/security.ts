import { SecurityEvent } from '../types/auth';

export class SecurityService {
  private static events: SecurityEvent[] = [];

  // Log security events
  static logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: new Date().toISOString(),
    };

    this.events.push(securityEvent);
    
    // Keep only last 100 events in memory
    if (this.events.length > 100) {
      this.events = this.events.slice(-100);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Security Event:', securityEvent);
    }

    // In production, you would send this to your monitoring service
    if (process.env.NODE_ENV === 'production' && event.severity === 'critical') {
      this.reportCriticalEvent(securityEvent);
    }
  }

  // Report critical security events
  private static reportCriticalEvent(event: SecurityEvent): void {
    // In a real implementation, this would send to your monitoring service
    // For now, we'll just log it
    console.error('CRITICAL SECURITY EVENT:', event);
  }

  // Validate file upload security
  static validateFileUpload(file: File): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // File type validation
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
      errors.push('Invalid file type. Only JPEG and PNG images are allowed.');
      this.logSecurityEvent({
        type: 'invalid_file_upload',
        severity: 'warning',
        message: `Invalid file type attempted: ${file.type}`,
        details: { fileName: file.name, fileType: file.type, fileSize: file.size }
      });
    }

    // File size validation (10MB max)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      errors.push('File size too large. Maximum size is 10MB.');
      this.logSecurityEvent({
        type: 'oversized_file_upload',
        severity: 'warning',
        message: `Oversized file upload attempted: ${file.size} bytes`,
        details: { fileName: file.name, fileSize: file.size, maxSize }
      });
    }

    // File name validation
    const dangerousPatterns = [
      /\.exe$/i, /\.bat$/i, /\.cmd$/i, /\.com$/i, /\.pif$/i, /\.scr$/i,
      /\.vbs$/i, /\.js$/i, /\.jar$/i, /\.php$/i, /\.asp$/i, /\.jsp$/i
    ];

    if (dangerousPatterns.some(pattern => pattern.test(file.name))) {
      errors.push('Invalid file name. File appears to be executable.');
      this.logSecurityEvent({
        type: 'dangerous_file_upload',
        severity: 'critical',
        message: `Dangerous file upload attempted: ${file.name}`,
        details: { fileName: file.name, fileType: file.type }
      });
    }

    return { valid: errors.length === 0, errors };
  }

  // Rate limiting check
  static checkRateLimit(action: string, userId: string, limit: number = 10, windowMs: number = 60000): boolean {
    const key = `${action}_${userId}`;
    const now = Date.now();
    
    // Get stored data
    const stored = localStorage.getItem(`rate_limit_${key}`);
    let attempts: number[] = [];
    
    if (stored) {
      try {
        attempts = JSON.parse(stored);
      } catch {
        attempts = [];
      }
    }

    // Filter out old attempts
    attempts = attempts.filter(timestamp => now - timestamp < windowMs);

    // Check if limit exceeded
    if (attempts.length >= limit) {
      this.logSecurityEvent({
        type: 'rate_limit_exceeded',
        severity: 'warning',
        message: `Rate limit exceeded for action: ${action}`,
        userId,
        details: { action, attempts: attempts.length, limit, windowMs }
      });
      return false;
    }

    // Add current attempt
    attempts.push(now);
    localStorage.setItem(`rate_limit_${key}`, JSON.stringify(attempts));

    return true;
  }

  // Content Security Policy validation and management
  static validateCSP(): boolean {
    // Check if CSP headers are properly set
    const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    const hasCSP = metaCSP !== null;

    if (!hasCSP) {
      this.logSecurityEvent({
        type: 'missing_csp',
        severity: 'warning',
        message: 'Content Security Policy not detected',
        details: { userAgent: navigator.userAgent }
      });
    }

    return hasCSP;
  }

  // Generate CSP header with Lambda URL support
  static generateCSPHeader(): string {
    const lambdaUrl = process.env.REACT_APP_BROADCAST_URL;
    let connectSrc = "'self' https://*.supabase.co https://api.line.me https://liffsdk.line-scdn.net https://*.line-scdn.net";

    // Add Lambda URL if configured
    if (lambdaUrl) {
      try {
        const url = new URL(lambdaUrl);
        connectSrc += ` ${url.origin}`;
      } catch (error) {
        console.warn('Invalid REACT_APP_BROADCAST_URL:', lambdaUrl);
      }
    }

    return [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' https://static.line-scdn.net https://liffsdk.line-scdn.net",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: blob: https:",
      `connect-src ${connectSrc}`,
      "frame-ancestors 'none'"
    ].join('; ');
  }

  // Inject or update CSP meta tag
  static injectCSP(): void {
    if (typeof document === 'undefined') return;

    // Remove existing CSP meta tag if present
    const existingCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (existingCSP) {
      existingCSP.remove();
    }

    // Create new CSP meta tag
    const meta = document.createElement('meta');
    meta.httpEquiv = 'Content-Security-Policy';
    meta.content = this.generateCSPHeader();
    document.head.appendChild(meta);

    this.logSecurityEvent({
      type: 'csp_injected',
      severity: 'info',
      message: 'Content Security Policy injected',
      details: { csp: meta.content }
    });
  }

  // Update existing CSP to include Lambda URL
  static updateCSPForLambda(): boolean {
    const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    const lambdaUrl = process.env.REACT_APP_BROADCAST_URL;

    if (!metaCSP || !lambdaUrl) {
      return false;
    }

    try {
      const url = new URL(lambdaUrl);
      const currentCSP = metaCSP.getAttribute('content') || '';

      // Check if Lambda URL is already in CSP
      if (currentCSP.includes(url.origin)) {
        return true;
      }

      // Add Lambda URL to connect-src
      const updatedCSP = currentCSP.replace(
        /connect-src ([^;]+)/,
        `connect-src $1 ${url.origin}`
      );

      metaCSP.setAttribute('content', updatedCSP);

      this.logSecurityEvent({
        type: 'csp_updated_for_lambda',
        severity: 'info',
        message: 'CSP updated to include Lambda URL',
        details: { lambdaUrl: url.origin, updatedCSP }
      });

      return true;
    } catch (error) {
      this.logSecurityEvent({
        type: 'csp_update_failed',
        severity: 'warning',
        message: 'Failed to update CSP for Lambda URL',
        details: { lambdaUrl, error: error instanceof Error ? error.message : 'Unknown error' }
      });
      return false;
    }
  }

  // Detect potential XSS attempts
  static detectXSS(input: string): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
      /<embed\b[^>]*>/gi,
      /data:text\/html/gi,
    ];

    const hasXSS = xssPatterns.some(pattern => pattern.test(input));

    if (hasXSS) {
      this.logSecurityEvent({
        type: 'xss_attempt',
        severity: 'critical',
        message: 'Potential XSS attempt detected',
        details: { input: input.substring(0, 100) + '...' }
      });
    }

    return hasXSS;
  }

  // SQL injection detection
  static detectSQLInjection(input: string): boolean {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
      /(--|\/\*|\*\/)/gi,
      /(\b(SCRIPT|JAVASCRIPT|VBSCRIPT)\b)/gi,
    ];

    const hasSQLInjection = sqlPatterns.some(pattern => pattern.test(input));

    if (hasSQLInjection) {
      this.logSecurityEvent({
        type: 'sql_injection_attempt',
        severity: 'critical',
        message: 'Potential SQL injection attempt detected',
        details: { input: input.substring(0, 100) + '...' }
      });
    }

    return hasSQLInjection;
  }

  // Validate session integrity
  static validateSession(): boolean {
    try {
      // Check if session data is consistent
      const sessionData = localStorage.getItem('supabase.auth.token');
      if (!sessionData) return false;

      const parsed = JSON.parse(sessionData);
      const now = Date.now() / 1000;

      // Check if token is expired
      if (parsed.expires_at && parsed.expires_at < now) {
        this.logSecurityEvent({
          type: 'expired_session',
          severity: 'info',
          message: 'Session expired',
          details: { expiresAt: parsed.expires_at, currentTime: now }
        });
        return false;
      }

      return true;
    } catch (error) {
      this.logSecurityEvent({
        type: 'session_validation_error',
        severity: 'warning',
        message: 'Error validating session',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      });
      return false;
    }
  }

  // Get security events (for admin/debugging)
  static getSecurityEvents(): SecurityEvent[] {
    return [...this.events];
  }

  // Clear security events
  static clearSecurityEvents(): void {
    this.events = [];
  }

  // Initialize security monitoring
  static initialize(): void {
    // Validate and setup CSP
    const hasCSP = this.validateCSP();

    // If no CSP exists, inject one
    if (!hasCSP) {
      this.injectCSP();
    } else {
      // Update existing CSP to include Lambda URL if needed
      this.updateCSPForLambda();
    }

    // Monitor for suspicious activity
    this.setupSecurityMonitoring();

    this.logSecurityEvent({
      type: 'security_service_initialized',
      severity: 'info',
      message: 'Security service initialized',
      details: { userAgent: navigator.userAgent, timestamp: new Date().toISOString() }
    });
  }

  private static setupSecurityMonitoring(): void {
    // Monitor for rapid-fire requests
    let requestCount = 0;
    const requestWindow = 500; // 500ms

    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      requestCount++;
      
      setTimeout(() => requestCount--, requestWindow);
      
      if (requestCount > 20) { // More than 20 requests per second
        this.logSecurityEvent({
          type: 'suspicious_request_pattern',
          severity: 'warning',
          message: 'High frequency requests detected',
          details: { requestCount, window: requestWindow }
        });
      }

      return originalFetch(...args);
    };
  }
}
