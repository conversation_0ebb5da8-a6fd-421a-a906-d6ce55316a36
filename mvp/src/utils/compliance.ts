// GDPR and HIPAA compliance utilities

export class ComplianceService {
  // GDPR compliance tracking
  private static consentRecords: Map<string, {
    userId: string;
    consentType: string;
    timestamp: string;
    ipAddress?: string;
    userAgent?: string;
  }> = new Map();

  // Record user consent
  static recordConsent(userId: string, consentType: 'data_processing' | 'cookies' | 'analytics'): void {
    const consentId = `${userId}_${consentType}_${Date.now()}`;
    
    this.consentRecords.set(consentId, {
      userId,
      consentType,
      timestamp: new Date().toISOString(),
      ipAddress: this.getClientIP(),
      userAgent: navigator.userAgent,
    });

    // Store in localStorage for persistence
    const existingConsents = JSON.parse(localStorage.getItem('user_consents') || '[]');
    existingConsents.push({
      consentType,
      timestamp: new Date().toISOString(),
    });
    localStorage.setItem('user_consents', JSON.stringify(existingConsents));
  }

  // Check if user has given consent
  static hasConsent(userId: string, consentType: string): boolean {
    const consents = JSON.parse(localStorage.getItem('user_consents') || '[]');
    return consents.some((consent: any) => consent.consentType === consentType);
  }

  // Withdraw consent
  static withdrawConsent(userId: string, consentType: string): void {
    // Remove from memory
    const keysToDelete: string[] = [];
    this.consentRecords.forEach((record, key) => {
      if (record.userId === userId && record.consentType === consentType) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => {
      this.consentRecords.delete(key);
    });

    // Remove from localStorage
    const consents = JSON.parse(localStorage.getItem('user_consents') || '[]');
    const updatedConsents = consents.filter((consent: any) => consent.consentType !== consentType);
    localStorage.setItem('user_consents', JSON.stringify(updatedConsents));
  }

  // HIPAA compliance for medical data
  static sanitizeMedicalData(data: any): any {
    const sensitiveFields = [
      'patient_name', 'patient_id', 'ssn', 'date_of_birth', 'phone', 'email',
      'address', 'medical_record_number', 'account_number', 'license_number',
      'vehicle_identifier', 'device_identifier', 'web_url', 'ip_address',
      'biometric_identifier', 'full_face_photo', 'comparable_image'
    ];

    const sanitized = { ...data };

    // Remove or mask sensitive fields
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        delete sanitized[field];
      }
    });

    // Sanitize text fields for potential PHI
    if (sanitized.description) {
      sanitized.description = this.sanitizeTextForPHI(sanitized.description);
    }

    return sanitized;
  }

  // Sanitize text for potential PHI
  private static sanitizeTextForPHI(text: string): string {
    if (!text) return text;

    // Remove potential dates
    text = text.replace(/\b\d{1,2}[/\-]\d{1,2}[/\-]\d{2,4}\b/g, '[DATE]');
    
    // Remove potential phone numbers
    text = text.replace(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, '[PHONE]');
    
    // Remove potential SSNs
    text = text.replace(/\b\d{3}-\d{2}-\d{4}\b/g, '[SSN]');
    
    // Remove potential email addresses
    text = text.replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]');
    
    // Remove potential names (basic pattern matching)
    text = text.replace(/\b(Mr|Mrs|Ms|Dr|Doctor|Patient)\s+[A-Z][a-z]+\b/g, '[NAME]');
    
    return text;
  }

  // Data retention policy
  static checkDataRetention(): void {
    const retentionPeriod = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
    const now = Date.now();

    // Check localStorage for old data
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith('temp_') || key.startsWith('cache_')) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}');
          if (data.timestamp && (now - new Date(data.timestamp).getTime()) > retentionPeriod) {
            localStorage.removeItem(key);
          }
        } catch {
          // If parsing fails, remove the item
          localStorage.removeItem(key);
        }
      }
    });
  }

  // Generate privacy report for user
  static generatePrivacyReport(userId: string): {
    dataCollected: string[];
    consentHistory: any[];
    retentionPolicy: string;
    rights: string[];
  } {
    const consents = JSON.parse(localStorage.getItem('user_consents') || '[]');
    
    return {
      dataCollected: [
        'Profile information (name, avatar)',
        'Uploaded medical images (anonymized)',
        'Voting preferences and history',
        'Usage analytics (anonymized)',
        'Session and authentication data'
      ],
      consentHistory: consents,
      retentionPolicy: 'Data is retained for 30 days after account deletion or as required by law. Medical images are anonymized and may be retained for educational purposes.',
      rights: [
        'Right to access your data',
        'Right to rectification',
        'Right to erasure (right to be forgotten)',
        'Right to restrict processing',
        'Right to data portability',
        'Right to object to processing',
        'Right to withdraw consent'
      ]
    };
  }

  // Anonymize user data for analytics
  static anonymizeForAnalytics(data: any): any {
    const anonymized = { ...data };
    
    // Remove direct identifiers
    delete anonymized.userId;
    delete anonymized.lineUserId;
    delete anonymized.displayName;
    delete anonymized.email;
    delete anonymized.pictureUrl;
    
    // Hash any remaining identifiers
    if (anonymized.id) {
      anonymized.id = this.hashString(anonymized.id);
    }
    
    // Add anonymization timestamp
    anonymized._anonymized = new Date().toISOString();
    
    return anonymized;
  }

  // Simple hash function for anonymization
  private static hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  // Get client IP (for consent logging)
  private static getClientIP(): string {
    // In a real implementation, this would be handled server-side
    // For client-side, we can't reliably get the real IP
    return 'client-side-unknown';
  }

  // Validate data minimization
  static validateDataMinimization(data: any, purpose: string): { valid: boolean; issues: string[] } {
    const issues: string[] = [];
    
    // Define what data is necessary for each purpose
    const necessaryFields: Record<string, string[]> = {
      'create_post': ['image_url', 'description', 'user_id'],
      'submit_vote': ['post_id', 'brand', 'model', 'diameter', 'length', 'confidence', 'user_id'],
      'view_results': ['post_id'],
      'user_profile': ['line_user_id', 'display_name', 'role']
    };

    const required = necessaryFields[purpose] || [];
    const provided = Object.keys(data);

    // Check for unnecessary data
    provided.forEach(field => {
      if (!required.includes(field) && !field.startsWith('_')) {
        issues.push(`Unnecessary field '${field}' for purpose '${purpose}'`);
      }
    });

    return {
      valid: issues.length === 0,
      issues
    };
  }

  // Initialize compliance monitoring
  static initialize(): void {
    // Check data retention on startup
    this.checkDataRetention();

    // Set up periodic cleanup
    setInterval(() => {
      this.checkDataRetention();
    }, 24 * 60 * 60 * 1000); // Daily cleanup

    // Log compliance initialization
    console.log('Compliance service initialized');
  }
}
