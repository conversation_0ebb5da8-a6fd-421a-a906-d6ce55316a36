import DOMPurify from 'dompurify';
import * as yup from 'yup';
import { VoteFormData } from '../types/app';
import { ValidationResult } from '../types/auth';

export class ValidationService {
  // Image validation
  static validateImage(file: File): Promise<ValidationResult> {
    return new Promise((resolve) => {
      const errors: string[] = [];
      
      // File type validation
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        errors.push('Only JPEG and PNG images are allowed');
      }

      // File size validation (10MB max)
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        errors.push('Image size must be less than 10MB');
      }

      // File name validation
      if (!/^[a-zA-Z0-9._-]+$/.test(file.name)) {
        errors.push('Invalid file name. Only letters, numbers, dots, hyphens, and underscores are allowed');
      }

      // If basic validation fails, return early
      if (errors.length > 0) {
        resolve({ valid: false, errors });
        return;
      }

      // Image dimensions validation
      const img = new Image();
      img.onload = () => {
        resolve({ valid: errors.length === 0, errors });
      };
      img.onerror = () => {
        errors.push('Invalid image file');
        resolve({ valid: false, errors });
      };
      img.src = URL.createObjectURL(file);
    });
  }

  // Text sanitization
  static sanitizeText(input: string): string {
    if (!input) return '';
    
    // Remove potentially dangerous HTML/JS
    const cleaned = DOMPurify.sanitize(input, { 
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: [],
    });

    // Normalize whitespace
    return cleaned.replace(/\s+/g, ' ').trim();
  }

  // Medical data validation schemas
  static readonly implantBrandSchema = yup.string()
    .required('Implant brand is required')
    .min(2, 'Brand name must be at least 2 characters')
    .max(100, 'Brand name must be less than 100 characters')
    .matches(/^[a-zA-Z0-9\s\-&.]+$/, 'Brand name contains invalid characters');

  static readonly implantModelSchema = yup.string()
    .required('Implant model is required')
    .min(1, 'Model name must be at least 1 character')
    .max(100, 'Model name must be less than 100 characters')
    .matches(/^[a-zA-Z0-9\s\-+.]+$/, 'Model name contains invalid characters');

  static readonly implantDiameterSchema = yup.string()
    .required('Implant diameter is required')
    .matches(/^\d+(\.\d+)?$/, 'Diameter must be a valid number')
    .test('range', 'Diameter must be between 2.0 and 8.0mm', (value) => {
      if (!value) return false;
      const num = parseFloat(value);
      return num >= 2.0 && num <= 8.0;
    });

  static readonly implantLengthSchema = yup.string()
    .required('Implant length is required')
    .matches(/^\d+(\.\d+)?$/, 'Length must be a valid number')
    .test('range', 'Length must be between 6 and 25mm', (value) => {
      if (!value) return false;
      const num = parseFloat(value);
      return num >= 6 && num <= 25;
    });

  static readonly confidenceSchema = yup.number()
    .required('Confidence level is required')
    .integer('Confidence must be a whole number')
    .min(1, 'Confidence must be at least 1')
    .max(5, 'Confidence must be at most 5');

  static readonly postDescriptionSchema = yup.string()
    .max(1000, 'Description must be less than 1000 characters')
    .test('content', 'Description contains inappropriate content', (value) => {
      if (!value) return true;
      return !this.containsInappropriateContent(value);
    });

  // Vote submission validation
  static validateVoteSubmission(data: VoteFormData): ValidationResult {
    const errors: string[] = [];

    // Validate brand
    if (!data.brand) {
      errors.push('Brand is required');
    }

    // Validate custom brand if "Other" is selected
    if (data.brand === 'Other') {
      if (!data.custom_brand || data.custom_brand.trim() === '') {
        errors.push('Custom brand name is required when "Other" is selected');
      } else if (data.custom_brand.length < 2) {
        errors.push('Custom brand name must be at least 2 characters');
      } else if (data.custom_brand.length > 50) {
        errors.push('Custom brand name must be less than 50 characters');
      }
    }

    // Validate model
    if (!data.model || data.model.trim() === '') {
      errors.push('Model is required');
    } else if (data.model.length > 50) {
      errors.push('Model must be less than 50 characters');
    }

    // Validate diameter
    if (!data.diameter) {
      errors.push('Diameter is required');
    } else {
      const diameterNum = parseFloat(data.diameter);
      if (isNaN(diameterNum) || diameterNum <= 0 || diameterNum > 10) {
        errors.push('Diameter must be a valid number between 0 and 10mm');
      }
    }

    // Validate length
    if (!data.length) {
      errors.push('Length is required');
    } else {
      const lengthNum = parseFloat(data.length);
      if (isNaN(lengthNum) || lengthNum < 6 || lengthNum > 25) {
        errors.push('Length must be a valid number between 6 and 25mm');
      }
    }

    // Validate confidence
    if (data.confidence < 1 || data.confidence > 5) {
      errors.push('Confidence must be between 1 and 5');
    }

    // Validate additional notes
    if (data.additional_notes && data.additional_notes.length > 500) {
      errors.push('Notes must be less than 500 characters');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  // Post creation validation
  static validatePostCreation(description?: string): ValidationResult {
    if (!description) {
      return { valid: true, errors: [] };
    }

    try {
      this.postDescriptionSchema.validateSync(description);
      return { valid: true, errors: [] };
    } catch (error: any) {
      return {
        valid: false,
        errors: [error.message || 'Invalid description']
      };
    }
  }

  // Content filtering
  private static containsInappropriateContent(text: string): boolean {
    const inappropriatePatterns = [
      // Basic profanity (add more as needed)
      /\b(spam|scam|fake|fraud)\b/i,
      // Potential malicious content
      /<script|javascript:|data:/i,
      // Personal information patterns
      /\b\d{3}-\d{2}-\d{4}\b/, // SSN pattern
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // Email pattern in content
    ];

    return inappropriatePatterns.some(pattern => pattern.test(text));
  }

  // Rate limiting validation
  static validateRateLimit(action: string, userId: string): boolean {
    // Simple client-side rate limiting
    const key = `${action}_${userId}`;
    const now = Date.now();
    const stored = localStorage.getItem(key);
    
    if (!stored) {
      localStorage.setItem(key, JSON.stringify({ count: 1, timestamp: now }));
      return true;
    }

    try {
      const data = JSON.parse(stored);
      const timeDiff = now - data.timestamp;
      const hourInMs = 60 * 60 * 1000;

      // Reset if more than an hour has passed
      if (timeDiff > hourInMs) {
        localStorage.setItem(key, JSON.stringify({ count: 1, timestamp: now }));
        return true;
      }

      // Check limits
      const limits: Record<string, number> = {
        'create_post': 5,
        'submit_vote': 20,
        'upload_image': 10,
      };

      const limit = limits[action] || 100;
      
      if (data.count >= limit) {
        return false;
      }

      // Increment count
      localStorage.setItem(key, JSON.stringify({ count: data.count + 1, timestamp: data.timestamp }));
      return true;
    } catch {
      // If parsing fails, reset
      localStorage.setItem(key, JSON.stringify({ count: 1, timestamp: now }));
      return true;
    }
  }
}
