import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/database';

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL!;
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false
  }
});

// Helper function to handle Supabase errors
export const handleSupabaseError = (error: any): string => {
  if (!error) return 'Unknown error occurred';
  
  // Handle specific error types
  if (error.code === 'PGRST301') {
    return 'You do not have permission to perform this action';
  }
  
  if (error.code === '23505') {
    return 'This action has already been performed';
  }
  
  if (error.code === '23503') {
    return 'Referenced item does not exist';
  }
  
  // Return the error message or a generic message
  return error.message || 'An unexpected error occurred';
};

// Type-safe query builder helpers
export const createTypedQuery = (tableName: keyof Database['public']['Tables']) => {
  return supabase.from(tableName) as any;
};

// Export types for convenience
export type { Database } from '../types/database';
