import React, { createContext, useContext, useState, useEffect } from 'react';
import { liffService } from '../services/liff';
import { apiService } from '../services/api';
import { supabase } from '../lib/supabase';
import { AuthContextType, AuthUser, UserRole } from '../types/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      await liffService.init();
      
      if (liffService.isLoggedIn()) {
        const profile = await liffService.getProfile();

        // Use anonymous authentication for simplicity
        const { error: anonError } = await supabase.auth.signInAnonymously();

        if (anonError) {
          console.error('Anonymous auth error:', anonError);
          throw anonError;
        }

        // Store LINE user ID in localStorage for API calls
        localStorage.setItem('line_user_id', profile.userId);

        // Create or update user profile with LINE data
        const userData = await apiService.upsertUser({
          line_user_id: profile.userId,
          display_name: profile.displayName,
          avatar_url: profile.pictureUrl,
        });

        setUser({
          id: userData.id,
          lineUserId: userData.line_user_id,
          displayName: userData.display_name || profile.displayName,
          pictureUrl: userData.avatar_url || profile.pictureUrl,
          role: (userData.role as UserRole) || UserRole.BASIC_USER,
          verificationStatus: userData.verification_status || undefined,
        });
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async () => {
    try {
      setLoading(true);
      await liffService.login();
      // The page will reload after login, so we don't need to handle the response here
    } catch (error) {
      console.error('Login failed:', error);
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      await supabase.auth.signOut();
      await liffService.logout();
      localStorage.removeItem('line_user_id');
      setUser(null);
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    logout,
    isLoggedIn: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
