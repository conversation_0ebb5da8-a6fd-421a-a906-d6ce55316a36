// Client-side AWS service that calls serverless functions
export class AWSService {
  private static readonly API_BASE = '/api/aws-service';

  // Upload file to S3
  static async uploadToS3(file: File, bucket: string, key: string): Promise<{ url: string; key: string }> {
    try {
      // Convert file to base64 for transmission
      const base64 = await this.fileToBase64(file);
      
      const response = await fetch(this.API_BASE, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'uploadToS3',
          bucket,
          key,
          body: base64,
          contentType: file.type,
        }),
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      return { url: result.url, key: result.key };
    } catch (error) {
      console.error('S3 upload error:', error);
      throw error;
    }
  }

  // Get item from DynamoDB
  static async getFromDynamoDB(tableName: string, key: Record<string, any>): Promise<any> {
    try {
      const response = await fetch(this.API_BASE, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'getFromDynamoDB',
          tableName,
          key,
        }),
      });

      if (!response.ok) {
        throw new Error(`DynamoDB get failed: ${response.statusText}`);
      }

      const result = await response.json();
      return result.item;
    } catch (error) {
      console.error('DynamoDB get error:', error);
      throw error;
    }
  }

  // Put item to DynamoDB
  static async putToDynamoDB(tableName: string, item: Record<string, any>): Promise<void> {
    try {
      const response = await fetch(this.API_BASE, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'putToDynamoDB',
          tableName,
          item,
        }),
      });

      if (!response.ok) {
        throw new Error(`DynamoDB put failed: ${response.statusText}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'DynamoDB put failed');
      }
    } catch (error) {
      console.error('DynamoDB put error:', error);
      throw error;
    }
  }

  // Helper function to convert file to base64
  private static fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const base64 = reader.result as string;
        // Remove data:image/jpeg;base64, prefix
        const base64Data = base64.split(',')[1];
        resolve(base64Data);
      };
      reader.onerror = error => reject(error);
    });
  }
}

// Usage examples:
/*
// Upload file to S3
const file = document.getElementById('fileInput').files[0];
const { url } = await AWSService.uploadToS3(file, 'my-bucket', 'uploads/image.jpg');

// Get item from DynamoDB
const item = await AWSService.getFromDynamoDB('my-table', { id: '123' });

// Put item to DynamoDB
await AWSService.putToDynamoDB('my-table', { id: '123', name: 'John', email: '<EMAIL>' });
*/
