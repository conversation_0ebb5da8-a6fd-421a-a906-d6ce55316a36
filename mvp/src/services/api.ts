import { supabase, handleSupabaseError } from '../lib/supabase';
import { Post, Vote, VoteSummary, User, AdData, UserStats } from '../types/database';
import { VoteFormData } from '../types/app';
import LineMessagingService from './lineMessaging';

export class ApiService {
  // Posts
  static async createPost(imageFile: File, description?: string): Promise<Post> {
    try {
      // Upload image first
      const imageUrl = await this.uploadImage(imageFile);

      // Get current LINE user instead of Supabase user
      const currentUser = await this.getCurrentUser();
      if (!currentUser) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('posts')
        .insert({
          image_url: imageUrl,
          description: description || null,
          user_id: currentUser.id, // Use our users table ID
          status: 'active', // Default to active (visible on board)
        })
        .select()
        .single();

      if (error) throw error;

      // Trigger broadcast notification for new post
      try {
        await LineMessagingService.notifyNewPost();
      } catch (notificationError) {
        // Don't fail the post creation if notification fails
        console.warn('Failed to send broadcast notification:', notificationError);
      }

      return data;
    } catch (error) {
      console.error('Create post error:', error);
      throw new Error(handleSupabaseError(error));
    }
  }

  static async getPosts(): Promise<Post[]> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          vote_count:votes(count)
        `)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Transform the data to include vote count
      const postsWithVoteCount = (data || []).map(post => ({
        ...post,
        vote_count: post.vote_count?.[0]?.count || 0
      }));

      return postsWithVoteCount;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }

  static async getAllPosts(): Promise<Post[]> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          vote_count:votes(count)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Transform the data to include vote count
      const postsWithVoteCount = (data || []).map(post => ({
        ...post,
        vote_count: post.vote_count?.[0]?.count || 0
      }));

      return postsWithVoteCount;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }

  static async searchPosts(filters: { brand: string; customBrand: string; model: string }): Promise<Post[]> {
    try {
      if (!filters.brand && !filters.model) {
        return [];
      }

      // Normalize search terms (uppercase, remove spaces)
      const searchBrand = filters.brand === 'OTHER'
        ? filters.customBrand.toUpperCase().replace(/\s+/g, '')
        : filters.brand.toUpperCase().replace(/\s+/g, '');

      const searchModel = filters.model.toUpperCase().replace(/\s+/g, '');

      // Get all active posts with their votes first
      // Filter for status='active' AND not expired (expires_at > now)
      const { data: allData, error } = await supabase
        .from('posts')
        .select(`
          *,
          vote_count:votes(count),
          votes!inner(brand, model)
        `)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Filter and normalize data on the client side for more reliable matching
      const uniquePosts = new Map();

      (allData || []).forEach(post => {

        // Only include posts that are:
        // 1. Status = "active" (already filtered in query)
        // 3. Not already processed
        if (post.status !== 'active' || uniquePosts.has(post.id)) {
          return;
        }

        // Check if any vote matches the search criteria
        const hasMatchingVote = post.votes?.some((vote: any) => {
          // Normalize database values (uppercase, remove spaces)
          const dbBrand = vote.brand.toUpperCase().replace(/\s+/g, '');
          const dbModel = vote.model.toUpperCase().replace(/\s+/g, '');

          // Check brand match
          const brandMatches = !searchBrand || dbBrand.includes(searchBrand);

          // Check model match
          const modelMatches = !searchModel || dbModel.includes(searchModel);

          return brandMatches && modelMatches;
        });

        if (hasMatchingVote) {
          uniquePosts.set(post.id, {
            ...post,
            vote_count: post.vote_count?.[0]?.count || 0
          });
        }
      });

      return Array.from(uniquePosts.values());
    } catch (error) {
      console.error('Search posts error:', error);
      throw new Error(handleSupabaseError(error));
    }
  }

  static async getPost(id: string): Promise<Post> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          vote_count:votes(count)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      // Transform the data to include vote count
      const postWithVoteCount = {
        ...data,
        vote_count: data.vote_count?.[0]?.count || 0
      };

      return postWithVoteCount;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }

  static async getMyPosts(): Promise<Post[]> {
    try {
      // Get current LINE user instead of Supabase user
      const currentUser = await this.getCurrentUser();
      if (!currentUser) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          vote_count:votes(count)
        `)
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Transform the data to include vote count
      const postsWithVoteCount = (data || []).map(post => ({
        ...post,
        vote_count: post.vote_count?.[0]?.count || 0
      }));

      return postsWithVoteCount;
    } catch (error) {
      console.error('Get my posts error:', error);
      throw new Error(handleSupabaseError(error));
    }
  }

  // Debug function to test what status values are allowed
  static async testStatusValues(postId: string): Promise<void> {
    const testValues = ['active', 'inactive', 'published', 'draft', 'visible', 'hidden', 'enabled', 'disabled', 'public', 'private', 'live', 'archived'];

    console.log('Testing allowed status values...');

    for (const status of testValues) {
      try {
        const { error } = await supabase
          .from('posts')
          .update({ status })
          .eq('id', postId)
          .select()
          .single();

        if (!error) {
          console.log('✅ Status value ALLOWED:', status);
        }
      } catch (error: any) {
        if (error.code === '23514') {
          console.log('❌ Status value REJECTED:', status);
        }
      }
    }
  }

  static async togglePostStatus(postId: string): Promise<Post> {
    try {
      // Get current user to verify ownership
      const currentUser = await this.getCurrentUser();
      if (!currentUser) throw new Error('User not authenticated');

      // First get the current post to check ownership and current status
      const { data: currentPost, error: fetchError } = await supabase
        .from('posts')
        .select('*')
        .eq('id', postId)
        .eq('user_id', currentUser.id) // Ensure user owns the post
        .single();

      if (fetchError) throw fetchError;
      if (!currentPost) throw new Error('Post not found or you do not have permission to modify it');

      console.log('Current post status:', currentPost.status);

      // Database allows: 'active', 'expired', 'completed', 'deleted'
      // For toggle functionality, we'll use 'active' (visible on board) and 'completed' (hidden from board)
      let newStatus: string;

      if (currentPost.status === 'active') {
        newStatus = 'completed'; // Hide from board
      } else {
        newStatus = 'active'; // Show on board (for any other status)
      }

      console.log('Toggling status from', currentPost.status, 'to', newStatus);

      const { data, error } = await supabase
        .from('posts')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', postId)
        .eq('user_id', currentUser.id)
        .select()
        .single();

      if (error) {
        console.error('Status update failed:', error);
        // If the update fails, let's try a different approach
        throw new Error(`Cannot toggle status. Current: ${currentPost.status}, Attempted: ${newStatus}. Database constraint error.`);
      }

      console.log('Status successfully changed to:', data.status);
      return data;
    } catch (error) {
      console.error('Toggle post status error:', error);
      throw new Error(handleSupabaseError(error));
    }
  }

  // Votes
  static async submitVote(voteData: VoteFormData & { post_id: string }): Promise<Vote> {
    try {
      // Get current LINE user instead of Supabase user
      const currentUser = await this.getCurrentUser();
      if (!currentUser) throw new Error('User not authenticated');

      // Use custom brand if "Other" is selected, otherwise use the selected brand
      const finalBrand = voteData.brand === 'Other' && voteData.custom_brand
        ? voteData.custom_brand
        : voteData.brand;

      const { data, error } = await supabase
        .from('votes')
        .insert({
          post_id: voteData.post_id,
          user_id: currentUser.id,
          brand: finalBrand.replace(/[^a-zA-Z0-9]/g, ''),
          model: voteData.model.replace(/[^a-zA-Z0-9]/g, ''),
          diameter: voteData.diameter.toString(),
          length: voteData.length.toString(),
          confidence: voteData.confidence,
          additional_notes: voteData.additional_notes || null,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Submit vote error:', error);
      throw new Error(handleSupabaseError(error));
    }
  }

  static async getVotes(postId: string): Promise<Vote[]> {
    try {
      const { data, error } = await supabase
        .from('votes')
        .select('*')
        .eq('post_id', postId);

      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }

  static async getVoteSummary(postId: string): Promise<VoteSummary[]> {
    try {
      const { data, error } = await supabase
        .from('vote_summaries')
        .select('*')
        .eq('post_id', postId)
        .order('vote_count', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }

  static async hasUserVoted(postId: string): Promise<boolean> {
    try {
      // Get current LINE user instead of Supabase user
      const currentUser = await this.getCurrentUser();
      if (!currentUser) return false;

      const { data, error } = await supabase
        .from('votes')
        .select('id')
        .eq('post_id', postId)
        .eq('user_id', currentUser.id)
        .limit(1);

      if (error) throw error;
      return (data?.length || 0) > 0;
    } catch (error) {
      console.error('Error checking vote status:', error);
      return false;
    }
  }

  // Advertisement methods
  static async getAdsData(): Promise<AdData[]> {
    try {
      // Fetch the ads JSON file from Supabase storage
      const { data, error } = await supabase.storage
        .from('ads-implant-iden')
        .download('ads.json');

      if (error) {
        console.error('Error fetching ads data:', error);
        return [];
      }

      // Convert blob to text and parse JSON
      const text = await data.text();
      const adsJson = JSON.parse(text);

      // Transform image URLs to full Supabase storage URLs
      const adsWithFullUrls = adsJson.ads.map((ad: any) => ({
        ...ad,
        image_url: this.getAdImageUrl(ad.image_url)
      }));

      return adsWithFullUrls;
    } catch (error) {
      console.error('Error parsing ads data:', error);
      return [];
    }
  }

  static getAdImageUrl(filename: string): string {
    const { data } = supabase.storage
      .from('ads-implant-images')
      .getPublicUrl(filename);

    return data.publicUrl;
  }

  // User statistics methods
  static async getUserStats(): Promise<UserStats> {
    try {
      const currentUser = await this.getCurrentUser();
      if (!currentUser) {
        return { postsCount: 0, votesCount: 0, rank: 0, totalUsers: 0 };
      }

      // Try to use the database function for efficient stats calculation
      const { data: statsData, error: statsError } = await supabase
        .rpc('get_user_stats', { target_user_id: currentUser.id });

      if (!statsError && statsData && statsData.length > 0) {
        const stats = statsData[0];
        return {
          postsCount: stats.posts_count || 0,
          votesCount: stats.votes_count || 0,
          rank: stats.user_rank || 0,
          totalUsers: stats.total_users || 0
        };
      }

      // Fallback: manual calculation if database function doesn't exist
      console.warn('Database function not available, using fallback calculation:', statsError);

      // Get user's post count
      const { data: postsData, error: postsError } = await supabase
        .from('posts')
        .select('id')
        .eq('user_id', currentUser.id);

      if (postsError) throw postsError;

      // Get user's vote count
      const { data: votesData, error: votesError } = await supabase
        .from('votes')
        .select('id')
        .eq('user_id', currentUser.id);

      if (votesError) throw votesError;

      // Calculate ranking manually - include ALL users from users table
      const [allUsersResult, allVotesResult] = await Promise.all([
        supabase.from('users').select('id'),
        supabase.from('votes').select('user_id')
      ]);

      if (allUsersResult.error) throw allUsersResult.error;
      if (allVotesResult.error) throw allVotesResult.error;

      // Count votes for each user
      const userVoteCounts = new Map();

      // Initialize all users with 0 votes
      allUsersResult.data?.forEach(user => {
        userVoteCounts.set(user.id, 0);
      });

      // Add actual vote counts
      allVotesResult.data?.forEach(vote => {
        userVoteCounts.set(vote.user_id, (userVoteCounts.get(vote.user_id) || 0) + 1);
      });

      // Sort users by vote count (descending)
      const sortedUsers = Array.from(userVoteCounts.entries())
        .sort(([,a], [,b]) => b - a);

      // Find user's rank (1-based)
      const userRank = sortedUsers.findIndex(([userId]) => userId === currentUser.id) + 1;

      return {
        postsCount: postsData?.length || 0,
        votesCount: votesData?.length || 0,
        rank: userRank || allUsersResult.data?.length || 0,
        totalUsers: allUsersResult.data?.length || 0
      };
    } catch (error) {
      console.error('Error fetching user stats:', error);
      return { postsCount: 0, votesCount: 0, rank: 0, totalUsers: 0 };
    }
  }

  // Image upload
  static async uploadImage(file: File): Promise<string> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}.${fileExt}`;
      
      const { data, error } = await supabase.storage
        .from('xray-images')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) throw error;

      const { data: { publicUrl } } = supabase.storage
        .from('xray-images')
        .getPublicUrl(data.path);

      return publicUrl;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }

  // User management
  static async getCurrentUser(): Promise<User | null> {
    try {
      // Get LINE user ID from localStorage (set during authentication)
      const lineUserId = localStorage.getItem('line_user_id');
      if (!lineUserId) return null;

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('line_user_id', lineUserId)
        .single();

      if (error) {
        console.error('Error getting current user:', error);
        return null;
      }
      return data;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  static async upsertUser(userData: {
    line_user_id: string;
    display_name?: string;
    avatar_url?: string;
  }): Promise<User> {
    try {
      // First try to get existing user
      const { data: existingUser } = await supabase
        .from('users')
        .select('*')
        .eq('line_user_id', userData.line_user_id)
        .maybeSingle();

      if (existingUser) {
        // Update existing user
        const { data, error } = await supabase
          .from('users')
          .update({
            display_name: userData.display_name,
            avatar_url: userData.avatar_url,
            updated_at: new Date().toISOString(),
          })
          .eq('line_user_id', userData.line_user_id)
          .select()
          .single();

        if (error) throw error;
        return data;
      } else {
        // Create new user with required fields
        const { data, error } = await supabase
          .from('users')
          .insert({
            line_user_id: userData.line_user_id,
            display_name: userData.display_name || 'Unknown User',
            avatar_url: userData.avatar_url,
            role: 'basic_user',
            verification_status: 'unverified',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (error) throw error;
        return data;
      }
    } catch (error) {
      console.error('Error upserting user:', error);
      throw new Error(handleSupabaseError(error));
    }
  }
}

export const apiService = ApiService;
