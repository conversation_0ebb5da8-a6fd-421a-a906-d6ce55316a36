// LINE Messaging API service for broadcasting notifications

export class LineMessagingService {



  // Generate random broadcast message
  private static generateBroadcastMessage(): string {
    const messages = [
      'มีหมอสร้าง Post ใหม่ เข้ามาช่วยกันแยกทีสิ ! 🦷✨',
      'มี Case ใหม่มาแล้ว! มาช่วยกันดู Implant กันเถอะ 🔍🦷',
      'หมอคนไหนว่างมาช่วยดู Post ใหม่หน่อย! 👨‍⚕️👩‍⚕️✨',
      'Case ใหม่รออยู่! เข้ามาช่วยกันระบุ Implant กันเถอะ 🚀🦷',
      'มีงานใหม่! มาช่วยกันแยกประเภท Implant กันเถอะ 💪🔬',
    ];

    const emojis = ['🦷', '✨', '🔍', '👨‍⚕️', '👩‍⚕️', '🚀', '💪', '🔬', '⚡', '🎯'];
    
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    const randomEmoji = emojis[Math.floor(Math.random() * emojis.length)];
    
    return `${randomMessage} ${randomEmoji}`;
  }



  // Send broadcast message to multiple users
  static async sendBroadcastNotification(userCount: number = 150): Promise<boolean> {
    try {
      console.log('🔔 New post notification triggered!');
      console.log('👥 Target users:', userCount);

      // First test if serverless functions are working at all
      try {
        console.log('🧪 Testing serverless function availability...');
        const testResponse = await fetch('/api/test', {
          method: 'GET',
        });

        if (testResponse.ok) {
          const testResult = await testResponse.json();
          console.log('✅ Serverless functions are working:', testResult);
        } else {
          console.warn('⚠️ Serverless test failed:', testResponse.status);
        }
      } catch (testError) {
        console.warn('⚠️ Serverless test error:', testError);
      }

      // Try to call broadcast serverless function
      try {
        console.log('📡 Calling broadcast API...');

        const broadcastUrl = process.env.REACT_APP_BROADCAST_URL;
        if (!broadcastUrl) {
          console.warn('⚠️ REACT_APP_BROADCAST_URL not configured, skipping API call');
          throw new Error('Broadcast URL not configured');
        }

        // Debug CSP and environment
        console.log('🔍 Environment:', process.env.NODE_ENV);
        console.log('🔍 Broadcast URL:', broadcastUrl);

        const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
        if (cspMeta) {
          console.log('� Current CSP:', cspMeta.getAttribute('content'));

          // Update CSP to include our specific Lambda URL
          const currentCSP = cspMeta.getAttribute('content') || '';
          if (!currentCSP.includes('7osfagty6bslbusjyfykzbwl2q0dtlhn.lambda-url.ap-southeast-1.on.aws')) {
            console.log('🔧 Adding Lambda URL to CSP...');
            const updatedCSP = currentCSP.replace(
              'connect-src ',
              `connect-src https://7osfagty6bslbusjyfykzbwl2q0dtlhn.lambda-url.ap-southeast-1.on.aws `
            );
            cspMeta.setAttribute('content', updatedCSP);
            console.log('✅ Updated CSP:', updatedCSP);
          }
        } else {
          console.log('🔍 No CSP meta tag found');
        }

        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
          'X-AWS-Region': process.env.REACT_APP_AWS_REGION || 'ap-southeast-1'
        };

        // Add optional headers only if environment variables are set
        if (process.env.REACT_APP_AWS_ACCESS_KEY_ID) {
          headers['Authorization'] = `Bearer ${process.env.REACT_APP_AWS_ACCESS_KEY_ID}`;
        }

        if (process.env.REACT_APP_AWS_SECRET_ACCESS_KEY) {
          headers['X-AWS-Secret'] = process.env.REACT_APP_AWS_SECRET_ACCESS_KEY;
        }

        const response = await fetch(broadcastUrl, {
          method: 'POST',
          headers,
          body: JSON.stringify({ userCount }),
        });

        console.log('📡 Broadcast response status:', response.status);

        if (response.ok) {
          const result = await response.json();
          console.log('✅ Broadcast API success:', result);
          console.log('📱 Message:', result.messageText);
          console.log('👥 Sent to', result.userCount, 'users');
          return true;
        } else {
          const errorText = await response.text();
          console.warn('⚠️ Broadcast API failed:', response.status, errorText);
        }
      } catch (apiError) {
        console.warn('⚠️ Broadcast API error:', apiError);
      }

      // Fallback: Generate and log the message
      const message = this.generateBroadcastMessage();

      console.log('📱 Generated message:', message);
      console.log('🎯 Would broadcast to', userCount, 'LINE users');
      console.log('💡 Broadcast simulation mode - message logged successfully');

      return true;
    } catch (error) {
      console.error('❌ Failed to send broadcast notification:', error);
      // Don't fail post creation if notification fails
      return true;
    }
  }

  // Send notification about new post (called when post is created)
  static async notifyNewPost(): Promise<void> {
    try {
      console.log('🔔 Triggering new post notification...');
      await this.sendBroadcastNotification(150);
    } catch (error) {
      console.error('Failed to send new post notification:', error);
    }
  }

  // Test function for development
  static async testBroadcast(): Promise<void> {
    console.log('🧪 Testing broadcast notification...');
    const message = this.generateBroadcastMessage();
    console.log('📱 Test message:', message);
    console.log('👥 Would send to 150 random users');
  }
}

export default LineMessagingService;
