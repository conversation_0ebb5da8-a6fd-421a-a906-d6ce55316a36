import liff from '@line/liff';
import { LineProfile } from '../types/auth';

class LiffService {
  private initialized = false;
  private initPromise: Promise<void> | null = null;

  async init(): Promise<void> {
    if (this.initialized) return;
    
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this.performInit();
    return this.initPromise;
  }

  private async performInit(): Promise<void> {
    try {
      const liffId = process.env.REACT_APP_LIFF_ID;
      if (!liffId) {
        throw new Error('LIFF ID not configured');
      }

      await liff.init({ liffId });
      this.initialized = true;
      console.log('LIFF initialized successfully');
    } catch (error) {
      console.error('LIFF initialization failed:', error);
      this.initPromise = null;
      throw error;
    }
  }

  async login(): Promise<void> {
    await this.init();
    
    if (!liff.isLoggedIn()) {
      liff.login();
    }
  }

  async logout(): Promise<void> {
    await this.init();
    
    if (liff.isLoggedIn()) {
      liff.logout();
    }
  }

  async getProfile(): Promise<LineProfile> {
    await this.init();
    
    if (!liff.isLoggedIn()) {
      throw new Error('User not logged in');
    }

    return await liff.getProfile();
  }

  async getAccessToken(): Promise<string> {
    await this.init();

    if (!liff.isLoggedIn()) {
      throw new Error('User not logged in');
    }

    const token = liff.getAccessToken();
    if (!token) {
      throw new Error('Failed to get access token');
    }

    return token;
  }

  getUserId(): string {
    if (!this.initialized) {
      throw new Error('LIFF not initialized');
    }

    const context = liff.getContext();
    return context?.userId || '';
  }

  isLoggedIn(): boolean {
    if (!this.initialized) return false;
    return liff.isLoggedIn();
  }

  isInClient(): boolean {
    if (!this.initialized) return false;
    return liff.isInClient();
  }

  closeWindow(): void {
    if (this.initialized && liff.isInClient()) {
      liff.closeWindow();
    }
  }

  openWindow(url: string, external = true): void {
    if (this.initialized) {
      liff.openWindow({ url, external });
    }
  }

  async sendMessage(messages: any[]): Promise<void> {
    await this.init();
    
    if (liff.isInClient()) {
      return liff.sendMessages(messages);
    }
  }

  // Get OS and version info
  getOS(): string {
    if (!this.initialized) return 'unknown';

    const context = liff.getContext();
    // Note: os property may not be available in all LIFF versions
    return (context as any)?.os || 'unknown';
  }

  // Check if running in Line app
  isInLineApp(): boolean {
    return this.isInClient();
  }

  // Share target picker (if available)
  async shareTargetPicker(messages: any[]): Promise<void> {
    await this.init();

    if (liff.isApiAvailable('shareTargetPicker')) {
      await liff.shareTargetPicker(messages);
    } else {
      throw new Error('Share target picker not available');
    }
  }
}

export const liffService = new LiffService();
