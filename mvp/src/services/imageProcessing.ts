import { SecurityService } from '../utils/security';

export class ImageProcessingService {
  // Compress image before upload
  static async compressImage(file: File, maxWidth: number = 1920, maxHeight: number = 1080, quality: number = 0.8): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        try {
          // Calculate new dimensions
          let { width, height } = img;
          
          if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width *= ratio;
            height *= ratio;
          }

          canvas.width = width;
          canvas.height = height;

          // Draw and compress
          ctx?.drawImage(img, 0, 0, width, height);
          
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const compressedFile = new File([blob], file.name, {
                  type: file.type,
                  lastModified: Date.now(),
                });
                resolve(compressedFile);
              } else {
                reject(new Error('Failed to compress image'));
              }
            },
            file.type,
            quality
          );
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  // Validate image content (basic checks)
  static async validateImageContent(file: File): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];

    try {
      // Security validation first
      const securityCheck = SecurityService.validateFileUpload(file);
      if (!securityCheck.valid) {
        errors.push(...securityCheck.errors);
      }

      // Create image element for validation
      const img = new Image();
      const imageUrl = URL.createObjectURL(file);

      const validationResult = await new Promise<{ valid: boolean; errors: string[] }>((resolve) => {
        img.onload = () => {
          const localErrors: string[] = [];

          // Check aspect ratio (should be reasonable for X-rays)
          const aspectRatio = img.width / img.height;
          if (aspectRatio > 3 || aspectRatio < 0.33) {
            localErrors.push('Image aspect ratio seems unusual for medical X-rays');
          }

          URL.revokeObjectURL(imageUrl);
          resolve({ valid: localErrors.length === 0, errors: localErrors });
        };

        img.onerror = () => {
          URL.revokeObjectURL(imageUrl);
          resolve({ valid: false, errors: ['Invalid or corrupted image file'] });
        };

        img.src = imageUrl;
      });

      errors.push(...validationResult.errors);

    } catch (error) {
      errors.push('Failed to validate image content');
    }

    return { valid: errors.length === 0, errors };
  }

  // Extract basic image metadata
  static async extractImageMetadata(file: File): Promise<{
    width: number;
    height: number;
    size: number;
    type: string;
    lastModified: number;
  }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const imageUrl = URL.createObjectURL(file);

      img.onload = () => {
        const metadata = {
          width: img.width,
          height: img.height,
          size: file.size,
          type: file.type,
          lastModified: file.lastModified,
        };

        URL.revokeObjectURL(imageUrl);
        resolve(metadata);
      };

      img.onerror = () => {
        URL.revokeObjectURL(imageUrl);
        reject(new Error('Failed to load image for metadata extraction'));
      };

      img.src = imageUrl;
    });
  }

  // Create thumbnail for preview
  static async createThumbnail(file: File, size: number = 200): Promise<string> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        try {
          // Calculate thumbnail dimensions (square)
          const { width, height } = img;
          const minDimension = Math.min(width, height);

          canvas.width = size;
          canvas.height = size;

          // Center crop
          const sourceX = (width - minDimension) / 2;
          const sourceY = (height - minDimension) / 2;

          ctx?.drawImage(
            img,
            sourceX, sourceY, minDimension, minDimension,
            0, 0, size, size
          );

          const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.7);
          resolve(thumbnailDataUrl);
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => reject(new Error('Failed to create thumbnail'));
      img.src = URL.createObjectURL(file);
    });
  }

  // Remove EXIF data for privacy
  static async removeExifData(file: File): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        try {
          canvas.width = img.width;
          canvas.height = img.height;

          // Draw image to canvas (this removes EXIF data)
          ctx?.drawImage(img, 0, 0);

          canvas.toBlob(
            (blob) => {
              if (blob) {
                const cleanFile = new File([blob], file.name, {
                  type: file.type,
                  lastModified: Date.now(),
                });
                resolve(cleanFile);
              } else {
                reject(new Error('Failed to remove EXIF data'));
              }
            },
            file.type,
            0.95
          );
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => reject(new Error('Failed to process image'));
      img.src = URL.createObjectURL(file);
    });
  }

  // Process image for upload (compress, clean, validate)
  static async processImageForUpload(file: File): Promise<{
    processedFile: File;
    metadata: any;
    thumbnail: string;
  }> {
    try {
      // Validate first
      const validation = await this.validateImageContent(file);
      if (!validation.valid) {
        throw new Error(validation.errors.join(', '));
      }

      // Extract metadata
      const metadata = await this.extractImageMetadata(file);

      // Remove EXIF data for privacy
      const cleanFile = await this.removeExifData(file);

      // Compress if needed
      let processedFile = cleanFile;
      if (cleanFile.size > 2 * 1024 * 1024) { // If larger than 2MB
        processedFile = await this.compressImage(cleanFile, 1920, 1080, 0.8);
      }

      // Create thumbnail
      const thumbnail = await this.createThumbnail(processedFile);

      return {
        processedFile,
        metadata,
        thumbnail,
      };
    } catch (error) {
      throw new Error(`Image processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Validate medical image characteristics
  static validateMedicalImage(file: File): Promise<{ valid: boolean; warnings: string[] }> {
    return new Promise((resolve) => {
      const warnings: string[] = [];
      const img = new Image();
      const imageUrl = URL.createObjectURL(file);

      img.onload = () => {
        // Check if image looks like it could be a medical X-ray
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);

        try {
          // Sample pixels to check if image is predominantly grayscale
          const imageData = ctx?.getImageData(0, 0, img.width, img.height);
          if (imageData) {
            let colorVariance = 0;
            const sampleSize = Math.min(1000, imageData.data.length / 4);
            
            for (let i = 0; i < sampleSize * 4; i += 4) {
              const r = imageData.data[i];
              const g = imageData.data[i + 1];
              const b = imageData.data[i + 2];
              
              const variance = Math.abs(r - g) + Math.abs(g - b) + Math.abs(r - b);
              colorVariance += variance;
            }
            
            const avgVariance = colorVariance / sampleSize;
            
            if (avgVariance > 30) {
              warnings.push('Image appears to be in color. X-rays are typically grayscale.');
            }
          }

          // Check aspect ratio
          const aspectRatio = img.width / img.height;
          if (aspectRatio > 2 || aspectRatio < 0.5) {
            warnings.push('Unusual aspect ratio for dental X-ray');
          }

          // Check resolution
          if (img.width < 300 || img.height < 300) {
            warnings.push('Low resolution image may be difficult to assess');
          }

        } catch (error) {
          warnings.push('Could not analyze image characteristics');
        }

        URL.revokeObjectURL(imageUrl);
        resolve({ valid: true, warnings });
      };

      img.onerror = () => {
        URL.revokeObjectURL(imageUrl);
        resolve({ valid: false, warnings: ['Could not load image for analysis'] });
      };

      img.src = imageUrl;
    });
  }
}
