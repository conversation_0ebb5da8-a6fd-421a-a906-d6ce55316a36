// Database types for Supabase
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          line_user_id: string;
          display_name: string | null;
          avatar_url: string | null;
          role: string;
          verification_status: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          line_user_id: string;
          display_name?: string | null;
          avatar_url?: string | null;
          role?: string;
          verification_status?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          line_user_id?: string;
          display_name?: string | null;
          avatar_url?: string | null;
          role?: string;
          verification_status?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      posts: {
        Row: {
          id: string;
          user_id: string;
          image_url: string;
          description: string | null;
          status: string;
          created_at: string;
          updated_at: string;
          expires_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          image_url: string;
          description?: string | null;
          status?: string;
          created_at?: string;
          updated_at?: string;
          expires_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          image_url?: string;
          description?: string | null;
          status?: string;
          created_at?: string;
          updated_at?: string;
          expires_at?: string;
        };
      };
      votes: {
        Row: {
          id: string;
          post_id: string;
          user_id: string;
          brand: string;
          model: string;
          diameter: string;
          length: string;
          confidence: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          post_id: string;
          user_id: string;
          brand: string;
          model: string;
          diameter: string;
          length: string;
          confidence: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          post_id?: string;
          user_id?: string;
          brand?: string;
          model?: string;
          diameter?: string;
          length?: string;
          confidence?: number;
          created_at?: string;
        };
      };
    };
    Views: {
      vote_summaries: {
        Row: {
          post_id: string;
          brand: string;
          model: string;
          diameter: string;
          length: string;
          vote_count: number;
          avg_confidence: number;
          percentage: number;
        };
      };
    };
  };
}

// Application types
export interface Post {
  id: string;
  user_id: string;
  image_url: string;
  description?: string;
  status: string;
  created_at: string;
  updated_at: string;
  expires_at: string;
  vote_count?: number; // Added for displaying vote count in UI
}

export interface Vote {
  id: string;
  post_id: string;
  user_id: string;
  brand: string;
  model: string;
  diameter: string;
  length: string;
  confidence: number;
  created_at: string;
}

export interface VoteSummary {
  post_id: string;
  brand: string;
  model: string;
  diameter: string;
  length: string;
  vote_count: number;
  avg_confidence: number;
  percentage: number;
}

export interface User {
  id: string;
  line_user_id: string;
  display_name?: string;
  avatar_url?: string;
  role: string;
  verification_status?: string;
  created_at: string;
  updated_at: string;
}

export interface AdData {
  customer_id: string;
  customer_name: string;
  redirect_url: string;
  image_url: string;
}
