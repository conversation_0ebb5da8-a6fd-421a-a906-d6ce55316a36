// Authentication and authorization types

export enum UserRole {
  BASIC_USER = 'basic_user',
  VERIFIED_DOCTOR = 'verified_doctor',
  SPECIALIST = 'specialist',
  MODERATOR = 'moderator',
  ADMIN = 'admin'
}

export enum Permission {
  CREATE_POST = 'create_post',
  VOTE_ON_POST = 'vote_on_post',
  VIEW_DETAILED_RESULTS = 'view_detailed_results',
  MODERATE_CONTENT = 'moderate_content',
  ACCESS_ANALYTICS = 'access_analytics',
  MANAGE_USERS = 'manage_users',
  SYSTEM_ADMIN = 'system_admin'
}

export interface LineProfile {
  userId: string;
  displayName: string;
  pictureUrl?: string;
  statusMessage?: string;
}

export interface AuthUser {
  id: string;
  lineUserId: string;
  displayName: string;
  pictureUrl?: string;
  role: UserRole;
  verificationStatus?: string;
}

export interface AuthContextType {
  user: AuthUser | null;
  loading: boolean;
  login: () => void;
  logout: () => void;
  isLoggedIn: boolean;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
}

export interface SecurityEvent {
  type: string;
  severity: 'info' | 'warning' | 'critical';
  message: string;
  userId?: string;
  details?: Record<string, any>;
  timestamp: string;
}
