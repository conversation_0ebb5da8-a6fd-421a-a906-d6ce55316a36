// Application-specific types
import type { Post, VoteSummary } from './database';

export interface VoteFormData {
  brand: string;
  custom_brand?: string; // For when brand is "Other"
  model: string;
  diameter: string;
  length: string;
  confidence: number;
  additional_notes?: string;
}

export interface ImageUploadProps {
  onImageSelect: (file: File) => void;
  selectedImage?: File;
  onImageRemove?: () => void;
}

export interface PostCardProps {
  post: Post;
  onVote?: () => void;
  onViewResults?: () => void;
  showActions?: boolean;
  onToggleStatus?: (postId: string) => void;
  showStatusToggle?: boolean;
}

export interface VoteFormProps {
  onSubmit: (data: VoteFormData) => void;
  isLoading?: boolean;
}

export interface VoteResultsProps {
  summary: VoteSummary[];
}

export interface ErrorDisplayProps {
  error: string;
  onRetry?: () => void;
}

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  totalPages: number;
}

// Form validation types
export interface FormErrors {
  [key: string]: string | undefined;
}

// Constants
export const IMPLANT_BRANDS = [
  'Nobel Biocare',
  'Straumann',
  'Zimmer',
  'Dentsply Sirona',
  'Osstem',
  'Hiossen',
  'Megagen',
  'Other'
] as const;

export const IMPLANT_DIAMETERS = [
  '3.0',
  '3.3',
  '3.5',
  '4.0',
  '4.3',
  '4.5',
  '5.0',
  '6.0'
] as const;

export const IMPLANT_LENGTHS = [
  '6',
  '8',
  '10',
  '11.5',
  '13',
  '15',
  '16',
  '18'
] as const;

export type ImplantBrand = typeof IMPLANT_BRANDS[number];
export type ImplantDiameter = typeof IMPLANT_DIAMETERS[number];
export type ImplantLength = typeof IMPLANT_LENGTHS[number];

// Re-export database types for convenience
export type { Post, Vote, VoteSummary, User } from './database';
