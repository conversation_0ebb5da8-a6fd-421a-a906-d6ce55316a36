import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import VoteForm from '../../components/VoteForm';
import { IMPLANT_BRANDS, IMPLANT_DIAMETERS, IMPLANT_LENGTHS } from '../../types/app';

// Mock the validation service
jest.mock('../../utils/validation', () => ({
  ValidationService: {
    validateVoteSubmission: jest.fn(() => ({ valid: true, errors: [] })),
  },
}));

describe('VoteForm', () => {
  const mockOnSubmit = jest.fn();

  beforeEach(() => {
    mockOnSubmit.mockClear();
  });

  it('renders all form fields', () => {
    render(<VoteForm onSubmit={mockOnSubmit} />);

    expect(screen.getByLabelText(/implant brand/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/model/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/diameter/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/length/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/confidence level/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/additional notes/i)).toBeInTheDocument();
  });

  it('populates brand dropdown with correct options', () => {
    render(<VoteForm onSubmit={mockOnSubmit} />);

    const brandSelect = screen.getByLabelText(/implant brand/i);
    
    IMPLANT_BRANDS.forEach(brand => {
      expect(screen.getByRole('option', { name: brand })).toBeInTheDocument();
    });
  });

  it('populates diameter dropdown with correct options', () => {
    render(<VoteForm onSubmit={mockOnSubmit} />);

    const diameterSelect = screen.getByLabelText(/diameter/i);
    
    IMPLANT_DIAMETERS.forEach(diameter => {
      expect(screen.getByRole('option', { name: `${diameter}mm` })).toBeInTheDocument();
    });
  });

  it('populates length dropdown with correct options', () => {
    render(<VoteForm onSubmit={mockOnSubmit} />);

    const lengthSelect = screen.getByLabelText(/length/i);
    
    IMPLANT_LENGTHS.forEach(length => {
      expect(screen.getByRole('option', { name: `${length}mm` })).toBeInTheDocument();
    });
  });

  it('updates confidence level when slider is moved', async () => {
    const user = userEvent.setup();
    render(<VoteForm onSubmit={mockOnSubmit} />);

    const slider = screen.getByRole('slider');
    
    await user.click(slider);
    fireEvent.change(slider, { target: { value: '3' } });

    expect(screen.getByText('Confidence Level: 3/5')).toBeInTheDocument();
  });

  it('submits form with correct data', async () => {
    const user = userEvent.setup();
    render(<VoteForm onSubmit={mockOnSubmit} />);

    // Fill out the form
    await user.selectOptions(screen.getByLabelText(/implant brand/i), 'Nobel Biocare');
    await user.type(screen.getByLabelText(/model/i), 'Active');
    await user.selectOptions(screen.getByLabelText(/diameter/i), '4.0');
    await user.selectOptions(screen.getByLabelText(/length/i), '10');
    
    const slider = screen.getByRole('slider');
    fireEvent.change(slider, { target: { value: '4' } });
    
    await user.type(screen.getByLabelText(/additional notes/i), 'Test notes');

    // Submit the form
    await user.click(screen.getByRole('button', { name: /submit vote/i }));

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        brand: 'Nobel Biocare',
        model: 'Active',
        diameter: '4.0',
        length: '10',
        confidence: 4,
        additional_notes: 'Test notes',
      });
    });
  });

  it('prevents submission when required fields are empty', async () => {
    const user = userEvent.setup();
    render(<VoteForm onSubmit={mockOnSubmit} />);

    // Try to submit without filling required fields
    await user.click(screen.getByRole('button', { name: /submit vote/i }));

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('shows loading state when isLoading is true', () => {
    render(<VoteForm onSubmit={mockOnSubmit} isLoading={true} />);

    expect(screen.getByText(/submitting/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /submitting/i })).toBeDisabled();
  });

  it('limits additional notes to 500 characters', async () => {
    const user = userEvent.setup();
    render(<VoteForm onSubmit={mockOnSubmit} />);

    const notesField = screen.getByLabelText(/additional notes/i);
    const longText = 'a'.repeat(600);

    await user.type(notesField, longText);

    expect(notesField).toHaveValue(longText.substring(0, 500));
    expect(screen.getByText('500/500 characters')).toBeInTheDocument();
  });

  it('updates character count for additional notes', async () => {
    const user = userEvent.setup();
    render(<VoteForm onSubmit={mockOnSubmit} />);

    const notesField = screen.getByLabelText(/additional notes/i);
    
    await user.type(notesField, 'Test');

    expect(screen.getByText('4/500 characters')).toBeInTheDocument();
  });

  it('clears field errors when user starts typing', async () => {
    const user = userEvent.setup();
    
    // Mock validation to return errors initially
    const { ValidationService } = require('../../utils/validation');
    ValidationService.validateVoteSubmission.mockReturnValueOnce({
      valid: false,
      errors: ['Brand is required'],
    });

    render(<VoteForm onSubmit={mockOnSubmit} />);

    // Try to submit to trigger validation
    await user.click(screen.getByRole('button', { name: /submit vote/i }));

    // Start typing in brand field
    await user.selectOptions(screen.getByLabelText(/implant brand/i), 'Nobel Biocare');

    // Error should be cleared (this would need to be implemented in the component)
    // This test assumes the component clears errors on field change
  });
});
