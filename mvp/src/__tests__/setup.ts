import '@testing-library/jest-dom';

// Mock LINE LIFF
global.liff = {
  init: jest.fn(() => Promise.resolve()),
  isLoggedIn: jest.fn(() => true),
  login: jest.fn(),
  logout: jest.fn(),
  getProfile: jest.fn(() => Promise.resolve({
    userId: 'test-user-id',
    displayName: 'Test User',
    pictureUrl: 'https://example.com/avatar.jpg'
  })),
  getAccessToken: jest.fn(() => 'mock-access-token'),
  getContext: jest.fn(() => ({ userId: 'test-user-id' })),
  isInClient: jest.fn(() => true),
  closeWindow: jest.fn(),
  openWindow: jest.fn(),
  sendMessages: jest.fn(() => Promise.resolve()),
  isApiAvailable: jest.fn(() => true),
  shareTargetPicker: jest.fn(() => Promise.resolve()),
};

// Mock Supabase
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn(() => Promise.resolve({ 
        data: { user: { id: 'test-user-id' } } 
      })),
      signInWithIdToken: jest.fn(() => Promise.resolve({ 
        data: { user: { id: 'test-user-id' } } 
      })),
      signOut: jest.fn(() => Promise.resolve()),
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      single: jest.fn(() => Promise.resolve({ data: {}, error: null })),
    })),
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn(() => Promise.resolve({ 
          data: { path: 'test-path' }, 
          error: null 
        })),
        getPublicUrl: jest.fn(() => ({ 
          data: { publicUrl: 'https://example.com/image.jpg' } 
        })),
      })),
    },
  })),
}));

// Mock react-query
jest.mock('react-query', () => ({
  useQuery: jest.fn(() => ({
    data: null,
    isLoading: false,
    error: null,
    refetch: jest.fn(),
  })),
  useMutation: jest.fn(() => ({
    mutate: jest.fn(),
    isLoading: false,
    error: null,
    reset: jest.fn(),
  })),
  QueryClient: jest.fn(() => ({
    invalidateQueries: jest.fn(),
  })),
  QueryClientProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
  useParams: () => ({ postId: 'test-post-id' }),
  useSearchParams: () => [new URLSearchParams(), jest.fn()],
}));

// Mock react-hot-toast
jest.mock('react-hot-toast', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
  Toaster: () => null,
}));

// Mock react-dropzone
jest.mock('react-dropzone', () => ({
  useDropzone: jest.fn(() => ({
    getRootProps: () => ({}),
    getInputProps: () => ({}),
    isDragActive: false,
  })),
}));

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mock-url');
global.URL.revokeObjectURL = jest.fn();

// Mock Image constructor
global.Image = class {
  onload: (() => void) | null = null;
  onerror: (() => void) | null = null;
  width = 500;
  height = 500;
  src = '';
} as any;

// Mock Canvas API
global.HTMLCanvasElement.prototype.getContext = jest.fn(() => ({
  drawImage: jest.fn(),
  getImageData: jest.fn(() => ({
    data: new Uint8ClampedArray(4),
  })),
}));

global.HTMLCanvasElement.prototype.toBlob = jest.fn((callback) => {
  callback(new Blob([''], { type: 'image/jpeg' }));
});

global.HTMLCanvasElement.prototype.toDataURL = jest.fn(() => 'data:image/jpeg;base64,mock');

// Mock FileReader
global.FileReader = class {
  onload: ((event: any) => void) | null = null;
  onerror: (() => void) | null = null;
  result: string | ArrayBuffer | null = null;
  
  readAsDataURL() {
    setTimeout(() => {
      this.result = 'data:image/jpeg;base64,mock';
      if (this.onload) {
        this.onload({ target: { result: this.result } });
      }
    }, 0);
  }
} as any;

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock as any;

// Mock fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
  })
) as any;

// Suppress console warnings in tests
global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn(),
};
