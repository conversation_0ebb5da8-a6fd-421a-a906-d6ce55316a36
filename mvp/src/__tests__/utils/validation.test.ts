import { ValidationService } from '../../utils/validation';

describe('ValidationService', () => {
  describe('validateImage', () => {
    it('accepts valid JPEG image', async () => {
      const file = new File([''], 'test.jpg', { type: 'image/jpeg' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 }); // 1MB

      // Mock Image constructor
      const mockImage = {
        onload: null as any,
        onerror: null as any,
        width: 500,
        height: 500,
        src: '',
      };

      global.Image = jest.fn(() => mockImage) as any;
      global.URL.createObjectURL = jest.fn(() => 'mock-url');

      const validationPromise = ValidationService.validateImage(file);
      
      // Simulate image load
      setTimeout(() => {
        if (mockImage.onload) mockImage.onload();
      }, 0);

      const result = await validationPromise;
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('rejects invalid file type', async () => {
      const file = new File([''], 'test.txt', { type: 'text/plain' });
      
      const result = await ValidationService.validateImage(file);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Only JPEG and PNG images are allowed');
    });

    it('rejects oversized file', async () => {
      const file = new File([''], 'test.jpg', { type: 'image/jpeg' });
      Object.defineProperty(file, 'size', { value: 15 * 1024 * 1024 }); // 15MB

      const result = await ValidationService.validateImage(file);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Image size must be less than 10MB');
    });

    it('rejects invalid file name', async () => {
      const file = new File([''], 'test<script>.jpg', { type: 'image/jpeg' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 });

      const result = await ValidationService.validateImage(file);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Invalid file name. Only letters, numbers, dots, hyphens, and underscores are allowed');
    });

    it('rejects image with insufficient dimensions', async () => {
      const file = new File([''], 'test.jpg', { type: 'image/jpeg' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 });

      const mockImage = {
        onload: null as any,
        onerror: null as any,
        width: 50,
        height: 50,
        src: '',
      };

      global.Image = jest.fn(() => mockImage) as any;
      global.URL.createObjectURL = jest.fn(() => 'mock-url');

      const validationPromise = ValidationService.validateImage(file);
      
      setTimeout(() => {
        if (mockImage.onload) mockImage.onload();
      }, 0);

      const result = await validationPromise;
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Image must be at least 100x100 pixels');
    });
  });

  describe('sanitizeText', () => {
    it('removes HTML tags', () => {
      const input = '<script>alert("xss")</script>Hello World';
      const result = ValidationService.sanitizeText(input);
      expect(result).toBe('Hello World');
    });

    it('normalizes whitespace', () => {
      const input = '  Hello    World  ';
      const result = ValidationService.sanitizeText(input);
      expect(result).toBe('Hello World');
    });

    it('handles empty input', () => {
      expect(ValidationService.sanitizeText('')).toBe('');
      expect(ValidationService.sanitizeText(null as any)).toBe('');
      expect(ValidationService.sanitizeText(undefined as any)).toBe('');
    });
  });

  describe('validateVoteSubmission', () => {
    const validVoteData = {
      brand: 'Nobel Biocare',
      model: 'Active',
      diameter: '4.0',
      length: '10',
      confidence: 4,
    };

    it('accepts valid vote data', () => {
      const result = ValidationService.validateVoteSubmission(validVoteData);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('rejects missing brand', () => {
      const invalidData = { ...validVoteData, brand: '' };
      const result = ValidationService.validateVoteSubmission(invalidData);
      expect(result.valid).toBe(false);
      expect(result.errors.some(error => error.includes('brand'))).toBe(true);
    });

    it('rejects invalid confidence level', () => {
      const invalidData = { ...validVoteData, confidence: 6 };
      const result = ValidationService.validateVoteSubmission(invalidData);
      expect(result.valid).toBe(false);
      expect(result.errors.some(error => error.includes('confidence'))).toBe(true);
    });

    it('rejects invalid diameter', () => {
      const invalidData = { ...validVoteData, diameter: 'invalid' };
      const result = ValidationService.validateVoteSubmission(invalidData);
      expect(result.valid).toBe(false);
      expect(result.errors.some(error => error.includes('diameter'))).toBe(true);
    });

    it('accepts valid additional notes', () => {
      const dataWithNotes = { ...validVoteData, additional_notes: 'Some notes' };
      const result = ValidationService.validateVoteSubmission(dataWithNotes);
      expect(result.valid).toBe(true);
    });

    it('rejects overly long additional notes', () => {
      const dataWithLongNotes = { 
        ...validVoteData, 
        additional_notes: 'a'.repeat(600) 
      };
      const result = ValidationService.validateVoteSubmission(dataWithLongNotes);
      expect(result.valid).toBe(false);
      expect(result.errors.some(error => error.includes('notes'))).toBe(true);
    });
  });

  describe('validateRateLimit', () => {
    beforeEach(() => {
      localStorage.clear();
    });

    it('allows first action', () => {
      const result = ValidationService.validateRateLimit('test_action', 'user1');
      expect(result).toBe(true);
    });

    it('tracks multiple actions', () => {
      ValidationService.validateRateLimit('test_action', 'user1');
      ValidationService.validateRateLimit('test_action', 'user1');
      const result = ValidationService.validateRateLimit('test_action', 'user1');
      expect(result).toBe(true);
    });

    it('blocks when limit exceeded', () => {
      // Perform actions up to limit
      for (let i = 0; i < 100; i++) {
        ValidationService.validateRateLimit('test_action', 'user1');
      }
      
      // Next action should be blocked
      const result = ValidationService.validateRateLimit('test_action', 'user1');
      expect(result).toBe(false);
    });

    it('resets after time window', () => {
      // Mock localStorage to simulate old timestamp
      const oldData = {
        count: 100,
        timestamp: Date.now() - (2 * 60 * 60 * 1000) // 2 hours ago
      };
      localStorage.setItem('test_action_user1', JSON.stringify(oldData));

      const result = ValidationService.validateRateLimit('test_action', 'user1');
      expect(result).toBe(true);
    });
  });

  describe('validatePostCreation', () => {
    it('accepts valid description', () => {
      const result = ValidationService.validatePostCreation('Valid description');
      expect(result.valid).toBe(true);
    });

    it('accepts empty description', () => {
      const result = ValidationService.validatePostCreation();
      expect(result.valid).toBe(true);
    });

    it('rejects overly long description', () => {
      const longDescription = 'a'.repeat(1100);
      const result = ValidationService.validatePostCreation(longDescription);
      expect(result.valid).toBe(false);
    });

    it('rejects inappropriate content', () => {
      const result = ValidationService.validatePostCreation('This is spam content');
      expect(result.valid).toBe(false);
    });
  });
});
