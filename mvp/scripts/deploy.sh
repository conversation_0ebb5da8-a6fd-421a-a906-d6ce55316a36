#!/bin/bash

# Deployment script for Implant Voting System MVP
set -e

echo "🚀 Starting deployment process..."

# Check if environment is specified
if [ -z "$1" ]; then
    echo "Usage: ./deploy.sh [staging|production]"
    exit 1
fi

ENVIRONMENT=$1

# Validate environment
if [ "$ENVIRONMENT" != "staging" ] && [ "$ENVIRONMENT" != "production" ]; then
    echo "❌ Invalid environment. Use 'staging' or 'production'"
    exit 1
fi

echo "📋 Deploying to: $ENVIRONMENT"

# Check if required tools are installed
command -v node >/dev/null 2>&1 || { echo "❌ Node.js is required but not installed."; exit 1; }
command -v npm >/dev/null 2>&1 || { echo "❌ npm is required but not installed."; exit 1; }

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create one from .env.example"
    exit 1
fi

# Validate environment variables
echo "🔍 Validating environment variables..."
source .env

required_vars=(
    "REACT_APP_SUPABASE_URL"
    "REACT_APP_SUPABASE_ANON_KEY"
    "REACT_APP_LIFF_ID"
    "REACT_APP_LINE_CHANNEL_ID"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Required environment variable $var is not set"
        exit 1
    fi
done

echo "✅ Environment variables validated"

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Run tests
echo "🧪 Running tests..."
npm test -- --coverage --watchAll=false

if [ $? -ne 0 ]; then
    echo "❌ Tests failed. Deployment aborted."
    exit 1
fi

echo "✅ All tests passed"

# Build the application
echo "🔨 Building application for $ENVIRONMENT..."
if [ "$ENVIRONMENT" = "production" ]; then
    npm run build:prod
else
    npm run build:staging
fi

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Deployment aborted."
    exit 1
fi

echo "✅ Build completed successfully"

# Security check
echo "🔒 Running security checks..."
npm audit --audit-level moderate

if [ $? -ne 0 ]; then
    echo "⚠️  Security vulnerabilities found. Please review before deploying to production."
    if [ "$ENVIRONMENT" = "production" ]; then
        read -p "Continue with deployment? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "❌ Deployment aborted due to security concerns."
            exit 1
        fi
    fi
fi

# Deploy based on platform
echo "🚀 Deploying to $ENVIRONMENT..."

case "$ENVIRONMENT" in
    "staging")
        echo "Deploying to staging environment..."
        # Add staging deployment commands here
        if command -v vercel >/dev/null 2>&1; then
            vercel --prod=false
        elif command -v netlify >/dev/null 2>&1; then
            netlify deploy --dir=build
        else
            echo "⚠️  No deployment tool found. Please deploy manually."
            echo "Build files are in the 'build' directory"
        fi
        ;;
    "production")
        echo "Deploying to production environment..."
        # Add production deployment commands here
        if command -v vercel >/dev/null 2>&1; then
            vercel --prod
        elif command -v netlify >/dev/null 2>&1; then
            netlify deploy --prod --dir=build
        else
            echo "⚠️  No deployment tool found. Please deploy manually."
            echo "Build files are in the 'build' directory"
        fi
        ;;
esac

# Post-deployment checks
echo "🔍 Running post-deployment checks..."

# Wait a moment for deployment to propagate
sleep 10

# Check if the application is accessible (you would replace this with your actual URL)
if [ "$ENVIRONMENT" = "production" ]; then
    DEPLOY_URL="https://your-production-domain.com"
else
    DEPLOY_URL="https://your-staging-domain.com"
fi

echo "Checking deployment at: $DEPLOY_URL"

# Basic health check
if command -v curl >/dev/null 2>&1; then
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$DEPLOY_URL" || echo "000")
    if [ "$HTTP_STATUS" = "200" ]; then
        echo "✅ Deployment successful! Application is accessible."
    else
        echo "⚠️  Deployment may have issues. HTTP status: $HTTP_STATUS"
    fi
else
    echo "⚠️  curl not available. Please manually verify deployment."
fi

# Cleanup
echo "🧹 Cleaning up..."
# Remove any temporary files if needed

echo "🎉 Deployment process completed!"
echo ""
echo "📋 Deployment Summary:"
echo "   Environment: $ENVIRONMENT"
echo "   Build: ✅ Success"
echo "   Tests: ✅ Passed"
echo "   Deploy: ✅ Completed"
echo ""
echo "🔗 Next Steps:"
echo "   1. Verify the application works correctly"
echo "   2. Test LINE LIFF integration"
echo "   3. Check all features are working"
echo "   4. Monitor for any errors"
echo ""
echo "📚 Useful Commands:"
echo "   - View logs: npm run logs"
echo "   - Rollback: npm run rollback"
echo "   - Monitor: npm run monitor"
