#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Supabase for Implant Voting System...\n');

// Check if .env file exists
const envPath = path.join(__dirname, '..', '.env');
const envExamplePath = path.join(__dirname, '..', '.env.example');

if (!fs.existsSync(envPath)) {
  if (fs.existsSync(envExamplePath)) {
    fs.copyFileSync(envExamplePath, envPath);
    console.log('✅ Created .env file from .env.example');
  } else {
    console.log('❌ .env.example file not found');
    process.exit(1);
  }
}

console.log('\n📋 Supabase Setup Checklist:');
console.log('');
console.log('1. Create a new Supabase project at https://supabase.com');
console.log('2. Go to Settings > API to get your project URL and anon key');
console.log('3. Update your .env file with the following variables:');
console.log('   - REACT_APP_SUPABASE_URL=your_project_url');
console.log('   - REACT_APP_SUPABASE_ANON_KEY=your_anon_key');
console.log('');
console.log('4. Run the database migration:');
console.log('   - Go to SQL Editor in your Supabase dashboard');
console.log('   - Copy and paste the contents of supabase/migrations/001_initial_schema.sql');
console.log('   - Execute the SQL to create tables and policies');
console.log('');
console.log('5. Set up Storage:');
console.log('   - Go to Storage in your Supabase dashboard');
console.log('   - Create a new bucket called "xray-images"');
console.log('   - Set the bucket to public');
console.log('   - Configure the following storage policies:');
console.log('');

// Storage policies
const storagePolicies = `
-- Storage policies for xray-images bucket
CREATE POLICY "Anyone can view images" ON storage.objects
  FOR SELECT USING (bucket_id = 'xray-images');

CREATE POLICY "Authenticated users can upload images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'xray-images' 
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Users can update their own images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'xray-images' 
    AND auth.uid()::text = owner::text
  );

CREATE POLICY "Users can delete their own images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'xray-images' 
    AND auth.uid()::text = owner::text
  );
`;

// Write storage policies to file
const storagePolicyPath = path.join(__dirname, '..', 'supabase', 'storage-policies.sql');
fs.writeFileSync(storagePolicyPath, storagePolicies);
console.log('📄 Storage policies saved to supabase/storage-policies.sql');
console.log('');

console.log('6. Configure Authentication:');
console.log('   - Go to Authentication > Settings');
console.log('   - Enable "Confirm email" if desired');
console.log('   - Configure any additional auth providers');
console.log('');

console.log('7. Set up Edge Functions (optional):');
console.log('   - For advanced features like image processing');
console.log('   - For automated cleanup of expired posts');
console.log('');

console.log('🔧 Environment Variables to Configure:');
console.log('');
const envContent = fs.readFileSync(envPath, 'utf8');
const envLines = envContent.split('\n');

envLines.forEach(line => {
  if (line.startsWith('REACT_APP_SUPABASE_URL=') || line.startsWith('REACT_APP_SUPABASE_ANON_KEY=')) {
    const [key, value] = line.split('=');
    if (!value || value.includes('your_')) {
      console.log(`❌ ${key}=${value || 'NOT_SET'}`);
    } else {
      console.log(`✅ ${key}=***configured***`);
    }
  }
});

console.log('');
console.log('📚 Next Steps:');
console.log('1. Configure your environment variables');
console.log('2. Run the database migration');
console.log('3. Set up storage bucket and policies');
console.log('4. Test the connection with: npm start');
console.log('');
console.log('🔗 Useful Links:');
console.log('- Supabase Dashboard: https://supabase.com/dashboard');
console.log('- Documentation: https://supabase.com/docs');
console.log('- SQL Editor: https://supabase.com/dashboard/project/[your-project]/sql');
console.log('');

// Check if migration file exists
const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '001_initial_schema.sql');
if (fs.existsSync(migrationPath)) {
  console.log('✅ Database migration file found');
} else {
  console.log('❌ Database migration file not found');
}

console.log('\n🎉 Supabase setup guide complete!');
console.log('Remember to keep your API keys secure and never commit them to version control.');
