#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('📱 Setting up LINE LIFF for Implant Voting System...\n');

console.log('📋 LINE LIFF Setup Checklist:');
console.log('');

console.log('1. Create a LINE Developers Account:');
console.log('   - Go to https://developers.line.biz/');
console.log('   - Sign in with your LINE account');
console.log('   - Create a new provider (company/organization)');
console.log('');

console.log('2. Create a LINE Login Channel:');
console.log('   - In LINE Developers Console, create a new channel');
console.log('   - Select "LINE Login" as the channel type');
console.log('   - Fill in the required information:');
console.log('     * Channel name: "Implant Voting System"');
console.log('     * Channel description: "Dental implant identification voting platform"');
console.log('     * App type: Web app');
console.log('');

console.log('3. Configure Channel Settings:');
console.log('   - Go to your channel settings');
console.log('   - Note down your Channel ID');
console.log('   - Configure the following scopes:');
console.log('     * profile (to get user profile information)');
console.log('     * openid (for authentication)');
console.log('');

console.log('4. Create a LIFF App:');
console.log('   - In your LINE Login channel, go to the LIFF tab');
console.log('   - Click "Add" to create a new LIFF app');
console.log('   - Configure the LIFF app:');
console.log('     * LIFF app name: "Implant Voting"');
console.log('     * Size: Full');
console.log('     * Endpoint URL: https://your-domain.com (your deployed app URL)');
console.log('     * Scope: profile, openid');
console.log('     * Bot link feature: Off (unless you want a chatbot)');
console.log('');

console.log('5. Development Setup:');
console.log('   - For local development, you can use:');
console.log('     * Endpoint URL: http://localhost:3000');
console.log('     * Or use ngrok for HTTPS: https://your-ngrok-url.ngrok.io');
console.log('');

console.log('6. Update Environment Variables:');
const envPath = path.join(__dirname, '..', '.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  
  console.log('   Current configuration:');
  envLines.forEach(line => {
    if (line.startsWith('REACT_APP_LIFF_ID=') || line.startsWith('REACT_APP_LINE_CHANNEL_ID=')) {
      const [key, value] = line.split('=');
      if (!value || value.includes('your_')) {
        console.log(`   ❌ ${key}=${value || 'NOT_SET'}`);
      } else {
        console.log(`   ✅ ${key}=***configured***`);
      }
    }
  });
} else {
  console.log('   ❌ .env file not found. Run npm run setup:supabase first.');
}

console.log('');
console.log('7. LIFF App Configuration Details:');
console.log('   - LIFF ID: Copy from LIFF tab (format: 1234567890-abcdefgh)');
console.log('   - Channel ID: Copy from Basic settings tab');
console.log('   - Update your .env file:');
console.log('     * REACT_APP_LIFF_ID=your_liff_id');
console.log('     * REACT_APP_LINE_CHANNEL_ID=your_channel_id');
console.log('');

console.log('8. Testing Your LIFF App:');
console.log('   - Use LINE LIFF Inspector: https://liff-inspector.line.me/');
console.log('   - Enter your LIFF URL to test');
console.log('   - Or scan QR code with LINE app on mobile');
console.log('');

console.log('9. Production Deployment:');
console.log('   - Deploy your app to a production server');
console.log('   - Update LIFF Endpoint URL to your production domain');
console.log('   - Ensure HTTPS is enabled');
console.log('   - Test thoroughly on mobile devices');
console.log('');

console.log('🔧 LIFF Development Tips:');
console.log('');
console.log('• Use LIFF Inspector for debugging');
console.log('• Test on actual mobile devices, not just browser');
console.log('• LIFF apps must be served over HTTPS in production');
console.log('• Use liff.ready() to ensure LIFF is initialized');
console.log('• Handle both in-app and external browser scenarios');
console.log('');

console.log('📱 Mobile Testing:');
console.log('1. Add your LIFF URL to LINE chat');
console.log('2. Tap the URL to open in LIFF browser');
console.log('3. Test login and all features');
console.log('4. Check responsive design on different screen sizes');
console.log('');

console.log('🔗 Useful Resources:');
console.log('- LINE Developers: https://developers.line.biz/');
console.log('- LIFF Documentation: https://developers.line.biz/en/docs/liff/');
console.log('- LIFF Inspector: https://liff-inspector.line.me/');
console.log('- LINE Login Documentation: https://developers.line.biz/en/docs/line-login/');
console.log('');

// Create a test LIFF configuration file
const testConfig = {
  liffId: 'your_liff_id_here',
  channelId: 'your_channel_id_here',
  endpointUrl: 'http://localhost:3000',
  scopes: ['profile', 'openid'],
  testInstructions: [
    'Replace liffId and channelId with actual values',
    'Update endpointUrl for production deployment',
    'Test with LIFF Inspector before going live',
    'Ensure mobile compatibility'
  ]
};

const configPath = path.join(__dirname, '..', 'liff-config.json');
fs.writeFileSync(configPath, JSON.stringify(testConfig, null, 2));
console.log('📄 LIFF configuration template saved to liff-config.json');

console.log('\n🎉 LINE LIFF setup guide complete!');
console.log('Remember to test your LIFF app thoroughly on mobile devices.');
console.log('LIFF apps behave differently in the LINE app vs external browsers.');
