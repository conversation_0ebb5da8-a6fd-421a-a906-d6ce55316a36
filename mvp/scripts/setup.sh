#!/bin/bash

# Complete setup script for Implant Voting System MVP
set -e

echo "🦷 Implant Voting System MVP - Complete Setup"
echo "=============================================="
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="16.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ Node.js version $NODE_VERSION is too old. Please install Node.js 16+ and try again."
    exit 1
fi

echo "✅ Node.js version $NODE_VERSION detected"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm and try again."
    exit 1
fi

echo "✅ npm detected"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo ""
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created"
    echo "⚠️  Please edit .env file with your actual configuration values"
else
    echo "✅ .env file already exists"
fi

# Run setup scripts
echo ""
echo "🔧 Running setup scripts..."

# Setup Supabase
echo "Setting up Supabase..."
node scripts/setup-supabase.js

# Setup LINE LIFF
echo ""
echo "Setting up LINE LIFF..."
node scripts/setup-line.js

# Run tests to ensure everything is working
echo ""
echo "🧪 Running tests..."
npm test -- --watchAll=false

if [ $? -ne 0 ]; then
    echo "⚠️  Some tests failed. Please review and fix before proceeding."
else
    echo "✅ All tests passed"
fi

# Build the application to ensure it compiles
echo ""
echo "🔨 Testing build process..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please check your configuration and try again."
    exit 1
fi

echo "✅ Build successful"

# Clean up build files (we just wanted to test)
rm -rf build

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Edit .env file with your actual configuration:"
echo "   - Supabase URL and API key"
echo "   - LINE LIFF ID and Channel ID"
echo ""
echo "2. Set up your Supabase database:"
echo "   - Run the SQL migration in your Supabase dashboard"
echo "   - Create the storage bucket for images"
echo "   - Configure RLS policies"
echo ""
echo "3. Configure your LINE LIFF app:"
echo "   - Set the endpoint URL to your deployed domain"
echo "   - Configure the required scopes"
echo ""
echo "4. Start development:"
echo "   npm start"
echo ""
echo "5. Deploy to production:"
echo "   ./scripts/deploy.sh production"
echo ""
echo "📚 Documentation:"
echo "   - README.md - General information"
echo "   - DEPLOYMENT.md - Deployment guide"
echo "   - guidelines/ - Implementation guidelines"
echo ""
echo "🆘 Need help?"
echo "   - Check the documentation"
echo "   - Review the setup scripts output above"
echo "   - Ensure all environment variables are configured"
echo ""
echo "Happy coding! 🚀"
