// AWS Lambda function for LINE broadcasting
export const handler = async (event, context) => {
  console.log('🔔 Lambda function invoked with event:', JSON.stringify(event));
  console.log('🔍 Request method:', event.requestContext?.http?.method || event.httpMethod);
  console.log('🔍 Request headers:', JSON.stringify(event.headers));
  console.log('🔍 Request body:', event.body);

  // Handle CORS for API Gateway integration
  const headers = {
    'Access-Control-Allow-Origin': 'https://implantidentification.vercel.app',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-AWS-Secret, X-AWS-Region',
    'Access-Control-Max-Age': '86400'
  };

  // Get HTTP method (works for both API Gateway and Lambda Function URL)
  const httpMethod = event.requestContext?.http?.method || event.httpMethod;
  console.log('🔍 Detected HTTP method:', httpMethod);

  // Handle OPTIONS request (CORS preflight)
  if (httpMethod === 'OPTIONS') {
    console.log('✅ Handling OPTIONS preflight request');
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  // Only allow POST requests
  if (httpMethod !== 'POST') {
    console.log('❌ Method not allowed:', httpMethod);
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({
        success: false,
        error: `Method ${httpMethod} not allowed. Only POST is supported.`
      })
    };
  }

  console.log('✅ Processing POST request');

  const body = JSON.parse(event.body || '{}');
  const { userCount = 150 } = body;
  const channelAccessToken = process.env.LINE_CHANNEL_ACCESS_TOKEN;

  console.log('🔔 Broadcast request received for', userCount, 'users');

  if (!channelAccessToken) {
    console.log('❌ LINE Channel Access Token not configured');
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'LINE Channel Access Token not configured'
      })
    };
  }

  // Generate random message
  const messages = [
    'มีหมอสร้าง Post ใหม่ เข้ามาช่วยกันแยกทีสิ ! 🦷✨',
    'มี Case ใหม่มาแล้ว! มาช่วยกันดู Implant กันเถอะ 🔍🦷',
    'หมอคนไหนว่างมาช่วยดู Post ใหม่หน่อย! 👨‍⚕️👩‍⚕️✨',
    'Case ใหม่รออยู่! เข้ามาช่วยกันระบุ Implant กันเถอะ 🚀🦷',
    'มีงานใหม่! มาช่วยกันแยกประเภท Implant กันเถอะ 💪🔬',
  ];

  const emojis = ['🦷', '✨', '🔍', '👨‍⚕️', '👩‍⚕️', '🚀', '💪', '🔬', '⚡', '🎯'];
  
  const randomMessage = messages[Math.floor(Math.random() * messages.length)];
  const randomEmoji = emojis[Math.floor(Math.random() * emojis.length)];
  const finalMessage = `${randomMessage} ${randomEmoji}`;

  console.log('📱 Generated message:', finalMessage);
  console.log('👥 Target users:', userCount);

  try {
    // Make LINE Messaging API broadcast request
    const response = await fetch('https://api.line.me/v2/bot/message/broadcast', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${channelAccessToken}`
      },
      body: JSON.stringify({
        messages: [{
          type: 'text',
          text: finalMessage
        }]
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('❌ LINE API Error:', errorData);
      return {
        statusCode: response.status,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'Failed to broadcast message',
          details: errorData
        })
      };
    }

    console.log('✅ Broadcast successful');
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        message: 'Broadcast sent successfully',
        userCount: userCount,
        messageText: finalMessage
      })
    };

  } catch (error) {
    console.error('❌ Error broadcasting message:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Internal server error',
        details: error.message
      })
    };
  }
};
