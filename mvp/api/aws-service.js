// Serverless function for AWS operations
import AWS from 'aws-sdk';

// Configure AWS
AWS.config.update({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
});

const s3 = new AWS.S3();
const dynamodb = new AWS.DynamoDB.DocumentClient();

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    const { action, ...params } = req.body;

    switch (action) {
      case 'uploadToS3':
        return await handleS3Upload(req, res, params);
      
      case 'getFromDynamoDB':
        return await handleDynamoDBGet(req, res, params);
      
      case 'putToDynamoDB':
        return await handleDynamoDBPut(req, res, params);
      
      default:
        return res.status(400).json({ error: 'Invalid action' });
    }
  } catch (error) {
    console.error('AWS service error:', error);
    return res.status(500).json({ error: error.message });
  }
}

async function handleS3Upload(req, res, { bucket, key, body, contentType }) {
  try {
    const params = {
      Bucket: bucket,
      Key: key,
      Body: body,
      ContentType: contentType,
    };

    const result = await s3.upload(params).promise();
    return res.status(200).json({ 
      success: true, 
      url: result.Location,
      key: result.Key 
    });
  } catch (error) {
    throw new Error(`S3 upload failed: ${error.message}`);
  }
}

async function handleDynamoDBGet(req, res, { tableName, key }) {
  try {
    const params = {
      TableName: tableName,
      Key: key,
    };

    const result = await dynamodb.get(params).promise();
    return res.status(200).json({ 
      success: true, 
      item: result.Item 
    });
  } catch (error) {
    throw new Error(`DynamoDB get failed: ${error.message}`);
  }
}

async function handleDynamoDBPut(req, res, { tableName, item }) {
  try {
    const params = {
      TableName: tableName,
      Item: item,
    };

    await dynamodb.put(params).promise();
    return res.status(200).json({ 
      success: true, 
      message: 'Item saved successfully' 
    });
  } catch (error) {
    throw new Error(`DynamoDB put failed: ${error.message}`);
  }
}
