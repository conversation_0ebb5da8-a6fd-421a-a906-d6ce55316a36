{"name": "implant-voting-liff", "version": "1.0.0", "description": "Line LIFF app for dental implant voting system", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@line/liff": "^2.22.0", "@supabase/supabase-js": "^2.38.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-router-dom": "^6.18.0", "react-scripts": "5.0.1", "dompurify": "^3.0.5", "yup": "^1.3.3"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/dompurify": "^3.0.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^4.9.5"}, "scripts": {"start": "HTTPS=true react-scripts start", "build": "CI=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "setup:all": "npm run setup:supabase && npm run setup:line", "setup:supabase": "node scripts/setup-supabase.js", "setup:line": "node scripts/setup-line.js", "test:liff": "node scripts/test-liff.js", "build:staging": "REACT_APP_ENV=staging CI=false npm run build", "build:prod": "REACT_APP_ENV=production CI=false GENERATE_SOURCEMAP=false npm run build", "deploy:vercel": "npx vercel --prod", "deploy:vercel:preview": "npx vercel", "deploy:netlify": "npx netlify deploy --prod --dir=build", "dev": "npm start", "preview": "npm run build && npx serve -s build -l 3000"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": "."}