{"version": 3, "file": "static/css/main.5f6f661d.css", "mappings": "AAAA,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,uEAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAAd,uCAAc,CAAd,iCAAc,CAAd,yIAAc,CAAd,6BAAc,CAAd,aAAc,CAAd,eAAc,CAAd,QAAc,CAAd,SAAc,CAAd,uBAAc,CA2BZ,WAIE,iHACF,CA9BF,2BAAmB,CAAnB,yBAAmB,CAAnB,WAAmB,CAAnB,eAAmB,CAAnB,SAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,SAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,+BAAmB,CAAnB,eAAmB,CAAnB,gBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,YAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,sBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,8BAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,yBAAmB,CAAnB,cAAmB,CAAnB,kNAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,wMAAmB,CAAnB,0CAAmB,EAAnB,+CAAmB,CAAnB,sCAAmB,CAAnB,8BAAmB,CAAnB,qCAAmB,CAAnB,gBAAmB,CAAnB,wBAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,+BAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,qCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,yBAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,iCAAmB,CAAnB,8BAAmB,CAAnB,kCAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,wBAAmB,CAAnB,0DAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,kDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,oDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,4BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,mCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,6FAAmB,CAAnB,yEAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,sEAAmB,CAAnB,kCAAmB,CAAnB,8BAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,6BAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,8CAAmB,CAAnB,mDAAmB,CAAnB,2CAAmB,CAAnB,4CAAmB,CAAnB,2CAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,mCAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,2BAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,mCAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iDAAmB,CAAnB,8BAAmB,CAAnB,oBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,wEAAmB,CAAnB,+FAAmB,CAAnB,4CAAmB,CAAnB,sDAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,2EAAmB,CAAnB,kGAAmB,CAAnB,yCAAmB,CAAnB,mMAAmB,CAAnB,kMAAmB,CAAnB,6IAAmB,CAAnB,mMAAmB,CAAnB,kDAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,6IAAmB,CAAnB,yFAAmB,CAAnB,uHAAmB,CAAnB,kDAAmB,CAAnB,wEAAmB,CAAnB,kDAAmB,CAAnB,0EAAmB,CAAnB,kDAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,0DAAmB,CAAnB,2DAAmB,CAiInB,kBACE,GACE,SACF,CACA,GACE,SACF,CACF,CAEA,mBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAGA,uCAEE,SACE,gCACF,CAGA,2FAME,cACF,CACF,CAQA,aACE,UACE,sBACF,CAEA,KACE,yBAA4B,CAC5B,oBACF,CACF,CAGA,+BACE,MACE,qBACF,CAEA,OACE,gBACF,CACF,CAGA,uCACE,iBAGE,kCAAqC,CACrC,qCAAuC,CACvC,mCACF,CACF,CA9MA,mDA+MA,CA/MA,oBA+MA,CA/MA,wDA+MA,CA/MA,2CA+MA,CA/MA,wBA+MA,CA/MA,sDA+MA,CA/MA,2CA+MA,CA/MA,wBA+MA,CA/MA,wDA+MA,CA/MA,0CA+MA,CA/MA,wBA+MA,CA/MA,wDA+MA,CA/MA,2CA+MA,CA/MA,wBA+MA,CA/MA,qDA+MA,CA/MA,iDA+MA,CA/MA,qBA+MA,CA/MA,oDA+MA,CA/MA,0CA+MA,CA/MA,wBA+MA,CA/MA,sDA+MA,CA/MA,0CA+MA,CA/MA,wBA+MA,CA/MA,sDA+MA,CA/MA,6CA+MA,CA/MA,wBA+MA,CA/MA,qDA+MA,CA/MA,8CA+MA,CA/MA,+CA+MA,CA/MA,+CA+MA,CA/MA,aA+MA,CA/MA,8CA+MA,CA/MA,+CA+MA,CA/MA,aA+MA,CA/MA,4CA+MA,CA/MA,+CA+MA,CA/MA,aA+MA,CA/MA,4CA+MA,CA/MA,gDA+MA,CA/MA,aA+MA,CA/MA,6CA+MA,CA/MA,8CA+MA,CA/MA,aA+MA,CA/MA,6CA+MA,CA/MA,oCA+MA,CA/MA,uFA+MA,CA/MA,iGA+MA,CA/MA,+FA+MA,CA/MA,kGA+MA,CA/MA,qFA+MA,CA/MA,+FA+MA,CA/MA,mDA+MA,CA/MA,oBA+MA,CA/MA,uDA+MA,CA/MA,mDA+MA,CA/MA,kDA+MA,CA/MA,kBA+MA,CA/MA,+HA+MA,CA/MA,wGA+MA,CA/MA,uEA+MA,CA/MA,wFA+MA,CA/MA,+CA+MA,CA/MA,wDA+MA,CA/MA,+CA+MA,CA/MA,yDA+MA,CA/MA,8CA+MA,CA/MA,uDA+MA,CA/MA,4CA+MA,CA/MA,yDA+MA,CA/MA,iDA+MA,CA/MA,uDA+MA,CA/MA,sDA+MA,CA/MA,2DA+MA,CA/MA,yDA+MA,CA/MA,iDA+MA,CA/MA,wBA+MA,CA/MA,wDA+MA,CA/MA,yCA+MA,CA/MA,gDA+MA", "sources": ["index.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n/* Custom CSS for mobile-first design */\n@layer base {\n  html {\n    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n\n  body {\n    margin: 0;\n    padding: 0;\n    background-color: #f9fafb;\n    color: #111827;\n    line-height: 1.6;\n  }\n\n  * {\n    box-sizing: border-box;\n  }\n}\n\n@layer components {\n  /* Safe area utilities for mobile devices */\n  .safe-area {\n    padding-top: env(safe-area-inset-top);\n    padding-bottom: env(safe-area-inset-bottom);\n    padding-left: env(safe-area-inset-left);\n    padding-right: env(safe-area-inset-right);\n  }\n\n  .safe-top {\n    padding-top: env(safe-area-inset-top);\n  }\n\n  .safe-bottom {\n    padding-bottom: env(safe-area-inset-bottom);\n  }\n\n  /* Custom button styles */\n  .btn-primary {\n    @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium;\n  }\n\n  .btn-secondary {\n    @apply bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors font-medium;\n  }\n\n  .btn-outline {\n    @apply border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium;\n  }\n\n  /* Form input styles */\n  .form-input {\n    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent;\n  }\n\n  .form-input-error {\n    @apply border-red-300 focus:ring-red-500;\n  }\n\n  /* Card styles */\n  .card {\n    @apply bg-white rounded-lg shadow-md overflow-hidden;\n  }\n\n  .card-hover {\n    @apply hover:shadow-lg transition-shadow;\n  }\n\n  /* Mobile-specific styles */\n  @media (max-width: 768px) {\n    .mobile-full-width {\n      width: 100vw;\n      margin-left: calc(-50vw + 50%);\n    }\n  }\n}\n\n@layer utilities {\n  /* Custom animations */\n  .animate-fade-in {\n    animation: fadeIn 0.3s ease-in-out;\n  }\n\n  .animate-slide-up {\n    animation: slideUp 0.3s ease-out;\n  }\n\n  .animate-pulse-slow {\n    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n  }\n\n  /* Text utilities */\n  .text-balance {\n    text-wrap: balance;\n  }\n\n  /* Scrollbar styling */\n  .scrollbar-thin {\n    scrollbar-width: thin;\n    scrollbar-color: #d1d5db #f3f4f6;\n  }\n\n  .scrollbar-thin::-webkit-scrollbar {\n    width: 6px;\n  }\n\n  .scrollbar-thin::-webkit-scrollbar-track {\n    background: #f3f4f6;\n  }\n\n  .scrollbar-thin::-webkit-scrollbar-thumb {\n    background-color: #d1d5db;\n    border-radius: 3px;\n  }\n\n  .scrollbar-thin::-webkit-scrollbar-thumb:hover {\n    background-color: #9ca3af;\n  }\n\n  /* Focus visible utilities */\n  .focus-visible-ring {\n    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2;\n  }\n}\n\n/* Keyframes */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes slideUp {\n  from {\n    transform: translateY(10px);\n    opacity: 0;\n  }\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n\n/* iOS specific fixes */\n@supports (-webkit-touch-callout: none) {\n  /* iOS Safari specific styles */\n  .ios-fix {\n    -webkit-overflow-scrolling: touch;\n  }\n  \n  /* Fix for iOS input zoom */\n  input[type=\"text\"],\n  input[type=\"email\"],\n  input[type=\"password\"],\n  input[type=\"number\"],\n  textarea,\n  select {\n    font-size: 16px;\n  }\n}\n\n/* Dark mode support (if needed in future) */\n@media (prefers-color-scheme: dark) {\n  /* Dark mode styles can be added here */\n}\n\n/* Print styles */\n@media print {\n  .no-print {\n    display: none !important;\n  }\n  \n  body {\n    background: white !important;\n    color: black !important;\n  }\n}\n\n/* High contrast mode support */\n@media (prefers-contrast: high) {\n  .card {\n    border: 2px solid #000;\n  }\n  \n  button {\n    border: 2px solid currentColor;\n  }\n}\n\n/* Reduced motion support */\n@media (prefers-reduced-motion: reduce) {\n  *,\n  *::before,\n  *::after {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n  }\n}\n"], "names": [], "sourceRoot": ""}