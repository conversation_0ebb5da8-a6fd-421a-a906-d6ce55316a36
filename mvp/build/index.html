<!doctype html><html lang="en"><head><meta charset="utf-8"/><link rel="icon" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiBmaWxsPSIjMjU2M2ViIi8+Cjx0ZXh0IHg9IjE2IiB5PSIyMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE2IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+8J+ltjwvdGV4dD4KPC9zdmc+Cg=="/><meta name="viewport" content="width=device-width,initial-scale=1,viewport-fit=cover"/><meta name="theme-color" content="#3B82F6"/><meta name="apple-mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-status-bar-style" content="default"/><meta name="apple-mobile-web-app-title" content="Implant Voting"/><title>Implant Voting System</title><meta name="description" content="Get expert opinions on dental implant identification"/><script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"/><link rel="apple-touch-icon" href="./logo192.png"/><link rel="manifest" href="./manifest.json"/><style>body{overscroll-behavior:none;-webkit-overflow-scrolling:touch;position:fixed;width:100%;height:100vh;overflow:hidden}#root{width:100%;height:100%;overflow-y:auto;-webkit-overflow-scrolling:touch}.loading-screen{position:fixed;top:0;left:0;width:100%;height:100%;background:#f9fafb;display:flex;align-items:center;justify-content:center;z-index:9999}.loading-spinner{width:40px;height:40px;border:4px solid #e5e7eb;border-top:4px solid #3b82f6;border-radius:50%;animation:spin 1s linear infinite}@keyframes spin{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}</style><script defer="defer" src="./static/js/main.09d1c6fb.js"></script><link href="./static/css/main.5f6f661d.css" rel="stylesheet"></head><body><noscript>You need to enable JavaScript to run this app.</noscript><div id="loading-screen" class="loading-screen"><div class="loading-spinner"></div></div><div id="root"></div><script>window.addEventListener("load",function(){setTimeout(function(){const e=document.getElementById("loading-screen");e&&(e.style.display="none")},1e3)})</script></body></html>