# Advertisement Carousel Setup Guide

## Overview
The advertisement carousel displays promotional content at the bottom of the Home page. It automatically cycles through ads every 5 seconds and supports manual navigation.

## Storage Setup

### 1. Create Supabase Storage Buckets

You need to create two storage buckets in your Supabase project:

#### Bucket 1: `ads-implant-iden`
- **Purpose**: Stores the ads configuration JSON file
- **Public**: Yes
- **File types**: JSON

#### Bucket 2: `ads-implant-images`  
- **Purpose**: Stores advertisement images
- **Public**: Yes
- **File types**: JPG, PNG, WebP, GIF

### 2. Upload Ads Configuration

Create a file named `ads.json` and upload it to the `ads-implant-iden` bucket:

```json
{
  "ads": [
    {
      "customer_id": "customer-001",
      "customer_name": "Straumann Thailand",
      "redirect_url": "https://www.straumann.com/th",
      "image_url": "straumann-banner.jpg"
    },
    {
      "customer_id": "customer-002", 
      "customer_name": "Nobel Biocare",
      "redirect_url": "https://www.nobelbiocare.com",
      "image_url": "nobel-biocare-banner.jpg"
    },
    {
      "customer_id": "customer-003",
      "customer_name": "Zimmer Biomet",
      "redirect_url": "https://www.zimmerbiomet.com",
      "image_url": "zimmer-biomet-banner.jpg"
    }
  ]
}
```

### 3. Upload Advertisement Images

Upload your advertisement images to the `ads-implant-images` bucket. The filenames should match the `image_url` values in your `ads.json` file.

**Recommended image specifications:**
- **Dimensions**: 400x160 pixels (2.5:1 aspect ratio)
- **Format**: JPG or PNG
- **File size**: Under 500KB for optimal loading
- **Quality**: High resolution for mobile displays

## Features

### Auto-Sliding Carousel
- Automatically advances every 5 seconds
- Pauses auto-play when user interacts manually
- Resumes auto-play after 10 seconds of inactivity

### Manual Navigation
- **Desktop**: Click arrow buttons or dots
- **Mobile**: Swipe left/right to navigate
- **Keyboard**: Arrow keys support (when focused)

### Responsive Design
- **Mobile**: 128px height (h-32)
- **Desktop**: 160px height (h-40)
- Optimized for mobile-first experience

### Click Tracking
- Each ad opens in a new tab when clicked
- Uses `window.open()` with security attributes
- Graceful error handling for invalid URLs

### Error Handling
- Fallback image for broken/missing images
- Graceful degradation if JSON file is missing
- Loading states with spinner
- No carousel shown if no ads available

## Customization

### Styling
The carousel uses Tailwind CSS classes and can be customized by modifying:
- `AdsCarousel.tsx` - Main component styling
- Transition duration (currently 500ms)
- Auto-advance interval (currently 5000ms)

### Animation Timing
```typescript
// Auto-advance interval
const interval = setInterval(() => {
  setCurrentSlide((prev) => (prev + 1) % ads.length);
}, 5000); // Change this value

// CSS transition duration
className="flex transition-transform duration-500 ease-in-out h-full"
//                                    ^^^^^^^^^^^
//                                    Change this value
```

## Testing

### Local Development
1. Ensure Supabase storage buckets are created
2. Upload `ads.json` to `ads-implant-iden` bucket
3. Upload corresponding images to `ads-implant-images` bucket
4. Start development server: `npm start`
5. Navigate to Home page to see carousel

### Production Deployment
1. Verify storage bucket permissions are set to public
2. Test image loading from different devices
3. Verify click tracking works correctly
4. Check responsive behavior on various screen sizes

## Troubleshooting

### Carousel Not Showing
- Check if `ads.json` exists in `ads-implant-iden` bucket
- Verify JSON format is valid
- Check browser console for API errors

### Images Not Loading
- Verify images exist in `ads-implant-images` bucket
- Check image filenames match `image_url` values in JSON
- Ensure bucket is set to public access

### Click Tracking Not Working
- Verify `redirect_url` values are valid URLs
- Check browser popup blocker settings
- Review browser console for JavaScript errors

## Security Considerations

- All external links open in new tabs with `noopener,noreferrer`
- Image URLs are validated through Supabase storage
- No user-generated content in ads system
- JSON file is served from trusted Supabase storage

## Performance Optimization

- Images use `loading="lazy"` for better performance
- Query caching (5 minutes stale time, 10 minutes cache time)
- Optimized image dimensions for mobile displays
- Minimal JavaScript bundle impact
