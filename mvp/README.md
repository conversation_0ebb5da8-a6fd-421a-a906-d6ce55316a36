# 🦷 Implant Voting System - MVP

A LINE LIFF application for dental professionals to collaboratively identify dental implants through community voting.

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ and npm
- Supabase account
- LINE Developers account
- Modern web browser

### Installation

1. **Clone and install dependencies:**
```bash
cd mvp
npm install
```

2. **Set up environment:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Run setup scripts:**
```bash
npm run setup:all
```

4. **Start development server:**
```bash
npm start
```

## 🏗️ Architecture

### Tech Stack
- **Frontend:** React 18 + TypeScript + Tailwind CSS
- **Authentication:** LINE LIFF + Supabase Auth
- **Database:** Supabase (PostgreSQL)
- **Storage:** Supabase Storage
- **State Management:** React Query
- **Validation:** Yup + DOMPurify
- **UI Components:** Headless UI + Heroicons

### Key Features
- ✅ LINE LIFF authentication integration
- ✅ Secure image upload with validation
- ✅ Real-time voting system
- ✅ Mobile-first responsive design
- ✅ HIPAA-compliant data handling
- ✅ Role-based access control
- ✅ Comprehensive input validation
- ✅ Rate limiting and security measures

## 📱 User Flow

1. **Authentication:** Users log in via LINE LIFF
2. **Create Post:** Upload X-ray images with optional descriptions
3. **Vote:** Dental professionals vote on implant identification
4. **Results:** View aggregated voting results with confidence levels
5. **Management:** Track personal posts and voting history

## 🔧 Configuration

### Environment Variables

```env
# Supabase Configuration
REACT_APP_SUPABASE_URL=your_supabase_project_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key

# LINE LIFF Configuration
REACT_APP_LIFF_ID=your_liff_app_id
REACT_APP_LINE_CHANNEL_ID=your_line_channel_id

# App Configuration
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/jpg
REACT_APP_POST_EXPIRY_HOURS=24
```

### Database Setup

1. Create a new Supabase project
2. Run the migration script in SQL Editor:
```sql
-- Copy contents of supabase/migrations/001_initial_schema.sql
```

3. Set up storage bucket:
   - Create bucket named "xray-images"
   - Set to public access
   - Apply storage policies from `supabase/storage-policies.sql`

### LINE LIFF Setup

1. Create LINE Login channel at https://developers.line.biz/
2. Create LIFF app with:
   - Size: Full
   - Endpoint URL: Your deployed app URL
   - Scopes: profile, openid
3. Update environment variables with LIFF ID and Channel ID

## 🛡️ Security Features

### Data Protection
- Input sanitization with DOMPurify
- SQL injection prevention via Supabase RLS
- XSS protection through CSP headers
- File upload validation and size limits
- Rate limiting on API endpoints

### Privacy Compliance
- GDPR-compliant data handling
- HIPAA-aware medical data processing
- Automatic post expiration (24 hours)
- Anonymous voting system
- Secure image storage with access controls

### Authentication & Authorization
- LINE LIFF integration for secure auth
- Role-based access control (RBAC)
- Row-level security (RLS) policies
- JWT token validation
- Session management

## 📊 Database Schema

### Core Tables
- `users` - User profiles and roles
- `posts` - X-ray image posts
- `votes` - Voting submissions
- `audit_logs` - Security and compliance logging

### Key Relationships
- Users can create multiple posts
- Users can vote once per post
- Posts automatically expire after 24 hours
- Vote summaries calculated via database views

## 🧪 Testing

### Run Tests
```bash
npm test
```

### Manual Testing Checklist
- [ ] LINE LIFF authentication flow
- [ ] Image upload and validation
- [ ] Vote submission and validation
- [ ] Results display and calculations
- [ ] Mobile responsiveness
- [ ] Error handling and edge cases

## 🚀 Deployment

### Build for Production
```bash
npm run build:prod
```

### Deployment Options

#### Vercel (Recommended)
```bash
npm run deploy:vercel
```

#### Netlify
```bash
npm run deploy:netlify
```

#### Manual Deployment
1. Build the application: `npm run build`
2. Upload `build/` folder to your hosting provider
3. Configure environment variables
4. Update LIFF endpoint URL to production domain

### Production Checklist
- [ ] Environment variables configured
- [ ] HTTPS enabled
- [ ] LIFF endpoint URL updated
- [ ] Database migrations applied
- [ ] Storage bucket configured
- [ ] Error monitoring set up
- [ ] Performance monitoring enabled

## 📈 Monitoring & Analytics

### Error Tracking
- Client-side error boundaries
- Supabase error logging
- User action audit trails

### Performance Monitoring
- React Query caching strategies
- Image optimization and lazy loading
- Mobile performance optimization

## 🔄 Maintenance

### Regular Tasks
- Monitor expired posts cleanup
- Review audit logs for security
- Update dependencies
- Performance optimization
- User feedback integration

### Database Maintenance
```sql
-- Clean up expired posts (run daily)
SELECT cleanup_expired_posts();

-- Monitor storage usage
SELECT pg_size_pretty(pg_total_relation_size('posts'));
```

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create feature branch
3. Implement changes with tests
4. Submit pull request
5. Code review and merge

### Code Standards
- TypeScript strict mode
- ESLint + Prettier formatting
- Component-based architecture
- Comprehensive error handling
- Mobile-first responsive design

## 📚 Documentation

### API Documentation
- Supabase auto-generated API docs
- Custom service layer documentation
- Authentication flow diagrams

### User Guides
- Setup and installation guide
- User manual for dental professionals
- Troubleshooting common issues

## 🆘 Support

### Common Issues
1. **LIFF not loading:** Check HTTPS and endpoint URL
2. **Authentication failed:** Verify LINE channel configuration
3. **Image upload failed:** Check file size and format
4. **Database errors:** Review RLS policies and permissions

### Getting Help
- Check documentation and guides
- Review GitHub issues
- Contact development team
- LINE LIFF community forums

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- LINE Corporation for LIFF platform
- Supabase for backend infrastructure
- React and TypeScript communities
- Dental professionals for domain expertise

---

**Note:** This is an MVP implementation focused on core functionality. Additional features like advanced analytics, AI-powered suggestions, and extended integrations can be added in future iterations.
