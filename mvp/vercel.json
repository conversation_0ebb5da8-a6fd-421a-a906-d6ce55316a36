{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "build"}}], "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' https://static.line-scdn.net https://liffsdk.line-scdn.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: blob: https:; connect-src 'self' https://*.supabase.co https://api.line.me https://liffsdk.line-scdn.net https://*.line-scdn.net https://7osfagty6bslbusjyfykzbwl2q0dtlhn.lambda-url.ap-southeast-1.on.aws; frame-ancestors 'none';"}]}], "env": {"REACT_APP_ENVIRONMENT": "production"}}