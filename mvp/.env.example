# Supabase Configuration
REACT_APP_SUPABASE_URL=your_supabase_project_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key

# Line LIFF Configuration
REACT_APP_LIFF_ID=your_liff_app_id
REACT_APP_LINE_CHANNEL_ID=your_line_channel_id
REACT_APP_LINE_CHANNEL_ACCESS_TOKEN=your_line_channel_access_token

# App Configuration
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/jpg
REACT_APP_POST_EXPIRY_HOURS=24

# Feature Flags (for MVP)
REACT_APP_ENABLE_NOTIFICATIONS=false
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_DEBUG_MODE=true

# Security
REACT_APP_API_BASE_URL=https://your-api-domain.com
REACT_APP_ENVIRONMENT=development
