-- Function to get user ranking based on vote count
-- This function calculates a user's rank compared to all other users based on their vote contributions

CREATE OR REPLACE FUNCTION get_user_ranking(target_user_id UUID)
RETURNS TABLE(rank INTEGER, total_users INTEGER) AS $$
BEGIN
  RETURN QUERY
  WITH all_users_with_votes AS (
    -- Include ALL users from users table, with their vote counts (0 if no votes)
    SELECT
      u.id as user_id,
      COALESCE(v.vote_count, 0) as vote_count
    FROM users u
    LEFT JOIN (
      SELECT user_id, COUNT(*) as vote_count
      FROM votes
      GROUP BY user_id
    ) v ON u.id = v.user_id
  ),
  ranked_users AS (
    SELECT
      user_id,
      vote_count,
      ROW_NUMBER() OVER (ORDER BY vote_count DESC, user_id) as user_rank
    FROM all_users_with_votes
  )
  SELECT
    COALESCE(ru.user_rank::INTEGER, (SELECT COUNT(*)::INTEGER FROM users)) as rank,
    (SELECT COUNT(*)::INTEGER FROM users) as total_users
  FROM ranked_users ru
  WHERE ru.user_id = target_user_id

  UNION ALL

  -- If user doesn't exist in users table, return last rank
  SELECT
    (SELECT COUNT(*)::INTEGER FROM users) as rank,
    (SELECT COUNT(*)::INTEGER FROM users) as total_users
  WHERE NOT EXISTS (
    SELECT 1 FROM users WHERE id = target_user_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_ranking(UUID) TO authenticated;

-- Example usage:
-- SELECT * FROM get_user_ranking('user-uuid-here');

-- Alternative simpler function for getting user stats in one call
CREATE OR REPLACE FUNCTION get_user_stats(target_user_id UUID)
RETURNS TABLE(
  posts_count INTEGER,
  votes_count INTEGER,
  user_rank INTEGER,
  total_users INTEGER
) AS $$
BEGIN
  RETURN QUERY
  WITH user_posts AS (
    SELECT COUNT(*)::INTEGER as post_count
    FROM posts
    WHERE user_id = target_user_id
  ),
  user_votes AS (
    SELECT COUNT(*)::INTEGER as vote_count
    FROM votes
    WHERE user_id = target_user_id
  ),
  all_users_with_votes AS (
    -- Include ALL users from users table, with their vote counts (0 if no votes)
    SELECT
      u.id as user_id,
      COALESCE(v.vote_count, 0) as vote_count
    FROM users u
    LEFT JOIN (
      SELECT user_id, COUNT(*) as vote_count
      FROM votes
      GROUP BY user_id
    ) v ON u.id = v.user_id
  ),
  ranked_users AS (
    SELECT
      user_id,
      vote_count,
      ROW_NUMBER() OVER (ORDER BY vote_count DESC, user_id) as user_rank
    FROM all_users_with_votes
  )
  SELECT
    up.post_count as posts_count,
    uv.vote_count as votes_count,
    COALESCE(ru.user_rank::INTEGER, (SELECT COUNT(*)::INTEGER FROM users)) as user_rank,
    (SELECT COUNT(*)::INTEGER FROM users) as total_users
  FROM user_posts up
  CROSS JOIN user_votes uv
  LEFT JOIN ranked_users ru ON ru.user_id = target_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_stats(UUID) TO authenticated;

-- Example usage:
-- SELECT * FROM get_user_stats('user-uuid-here');
